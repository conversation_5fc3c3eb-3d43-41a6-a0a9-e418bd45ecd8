import * as CONSTANTS from './constants.js';
import type { PrimitiveConst, StepConst, WorkflowConst } from './types.js';
import { dbAdmin } from "./firestoreAdminInit.js";


export function getNewId(identifiable: PrimitiveConst | StepConst | WorkflowConst | 'JOBSTEP') { // ATTENTION: added 'JOBSTEP' here
    const base = identifiable.toUpperCase();
    const normalized = base.endsWith('S') ? base.slice(0, -1) : base;
    const prefix = normalized + '-';
    const docRef = dbAdmin.collection(CONSTANTS.PRIMITIVES).doc(identifiable).collection(CONSTANTS.MEMBERS).doc();
    return prefix + docRef.id;
}


// ATTENTION: can't do this here until we can import types from schemas package
/* export async function getPrimitivesLib() {
    const [resourceTypes, resourceRoles, jobs] = await Promise.all([
        listPrimitives('resource-types'),
        listPrimitives('resource-roles'),
        listPrimitives('jobs'),
    ]);

    return {
        'resource-types': new Map(resourceTypes.items.map(item => [item.id, item])),
        'resource-roles': new Map(resourceRoles.items.map(item => [item.id, item])),
        'jobs': new Map(jobs.items.map(item => [item.id, item])),
    };
} */

export async function listPrimitives<T>(primitiveConst: PrimitiveConst, collection: string = CONSTANTS.PRIMITIVES): Promise<{ ok: true; items: T[] }> {
    const snap = await dbAdmin
        .collection(collection)
        .doc(primitiveConst)
        .collection(CONSTANTS.MEMBERS)
        .get();
    const items = snap.docs.map((d: { data: () => Record<string, unknown>; id: string }) => {
        const data = d.data() as T
        return {
            id: d.id,
            ...data
        }
    });
    return { ok: true as const, items }; // ATTENTION: is this wrapping necessary?
}