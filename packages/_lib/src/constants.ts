import { exec } from "child_process";

export const LESS_THAN = 'less-than' as const;

export const ROLE_LESS_THAN_TARGET = 'RER-08XaIEWYaVhCqdrrORY4' as const;

export const ROLE_EVALUATION = 'RER-rRRZNF7057E91qMPDvtF' as const;

export const JOB_LESS_THAN = 'JOB-ub1fy3byn1KOYefpQcAr' as const;

export const FORMAT_ApplicationJson = 'FORMAT-ApplicationJson' as const;

export const FORMAT_ApplicationJob = 'FORMAT-ApplicationJob' as const;

export const TP_PRIMITIVES = 'tp-primitives' as const;

export const TP_RESOURCES = 'tp-resources' as const;

export const RESOURCES = 'resources' as const;

export const PRIMITIVES = 'primitives' as const;

export const MEMBERS = 'members' as const;

export const SPECIALS = 'specials' as const;

export const FORMATS = 'formats' as const;

export const TYPES = 'types' as const;

export const ROLES = 'roles' as const;

export const JOBS = 'jobs' as const;

export const WORKFLOW_SPEC = 'workflow-spec' as const;

export const WORKFLOW = 'workflow' as const;

export const JOB = 'job' as const;

export const BRANCH = 'branch' as const;

export const WHILE = 'while' as const;

export const FOR = 'for' as const;

export const INPUT = 'input' as const;

export const OUTPUT = 'output' as const;

export const DELIMITER = '__' as const;

export const GRAPH_RUN_WORKFLOW = 'GraphRunWorkflow' as const;


export const CONSTANTS = {
    SCHEMA: {
        FormatData: 'FormatData',
        TypeData: 'TypeData',
        SignatureData: 'SignatureData',
        ImplementationData: 'ImplementationData',
        ExecutionStepData: 'ExecutionStepData',
        ResourceMap: 'ResourceMap',
    },
    STORAGE: {
        tp_primitives: 'tp-primitives',
        tp_resources: 'tp-resources',
        members: 'members',
        specials: 'specials',
        primitives: 'primitives',
        resources: 'resources',
    },
    PRIMITIVES: {
        formats: 'formats' as const,
        types: 'types' as const,
        roles: 'roles' as const,
        signatures: 'signatures' as const,
        implementations: 'implementations' as const
    },
    NON_PRIMITIVES: {
        workflow: 'workflow' as const,
        resources: 'resources' as const,
    },
    DIRECTION: {
        input: 'input',
        output: 'output',
    },
    STEP: {
        work: 'work',
        branch: 'branch',
        while: 'while',
        for: 'for',
    },
    ENGINE: {
        GraphRunWorkflow: 'GraphRunWorkflow',
    },
    SPECIALS: {
        FORMAT_ApplicationJson: 'FORMAT-ApplicationJson',
        FORMAT_ApplicationJob: 'FORMAT-ApplicationJob',
        FORMAT_Test_0: 'FORMAT-Test_0',
        TYPE_WorkflowSpec: 'TYPE-WorkflowSpec',
        ROLE_ManualCreation: 'ROLE-ManualCreation', // ATTENTION: legacy
        ROLE_LessThanTarget: 'ROLE-LessThanTarget', // ATTENTION: legacy
        SIGNATURE_Builder: 'SIGNATURE-Builder',
        SIGNATURE_Engine: 'SIGNATURE-Engine'
    }
} as const;