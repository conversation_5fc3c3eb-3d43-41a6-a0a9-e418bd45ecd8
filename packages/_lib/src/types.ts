import { CONSTANTS } from './constants.js';

export type PrimitiveConst = typeof CONSTANTS.PRIMITIVES.formats | typeof CONSTANTS.PRIMITIVES.types | typeof CONSTANTS.PRIMITIVES.roles | typeof CONSTANTS.PRIMITIVES.signatures | typeof CONSTANTS.PRIMITIVES.implementations;

export type StepConst = typeof CONSTANTS.STEP.work | typeof CONSTANTS.STEP.branch | typeof CONSTANTS.STEP.while | typeof CONSTANTS.STEP.for;

export type WorkflowConst = 'WORKFLOW' | 'WORKFLOWSPEC' | 'WORKSTEP' | 'EXECUTION'; // ATTENTION

export interface DryRunManagerType {
    dryRunMode: boolean;
    delay: number;
    drySocketMode: boolean;
}