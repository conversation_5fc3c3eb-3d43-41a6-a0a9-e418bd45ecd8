{"name": "@toolproof/_lib", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./constants": {"import": "./dist/constants.js", "types": "./dist/constants.d.ts"}, "./types": {"import": "./dist/types.js", "types": "./dist/types.d.ts"}, "./server": {"node": "./dist/firestoreAdminHelpers.js", "types": "./dist/firestoreAdminHelpers.d.ts"}}, "scripts": {"build": "tsc -b"}, "files": ["dist"], "devDependencies": {"@types/node": "^20.19.11", "json-schema-to-typescript": "^15.0.4", "ts-node": "^10.9.2", "typescript": "^5.9.3"}, "dependencies": {"firebase-admin": "^13.4.0"}}