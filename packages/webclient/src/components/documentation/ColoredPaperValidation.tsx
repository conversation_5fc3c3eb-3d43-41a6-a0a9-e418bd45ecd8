'use client';

import type { TypeDataJson } from '@toolproof/_schemas';
import type { UploadResult } from '@/types';
import { validatePrimitive, validateResource } from '@toolproof/_validator';
import { CONSTANTS } from '@toolproof/_lib/constants';
import Whitepaper from './coloredPapers/Whitepaper.json';
import Yellowpaper from './coloredPapers/Yellowpaper.json';
import WorkflowSpec_Resource from './coloredPapers/WorkflowSpec_Resource.json';
import { ColoredPaperSchema, WorkflowSpec_Schema } from '@toolproof/_schemas'
import { getNewId, uploadPrimitive } from '@/_lib/server/firebaseAdminHelpers';
import { useState } from 'react';

const ColoredPaperType = ColoredPaperSchema;

export default function ColoredPaperValidation() {

    // console.log('ColoredPaperType:', JSON.stringify(ColoredPaperType, null, 2));

    const ColoredPaperType_ = { ...ColoredPaperType, exposedSchema: { ...ColoredPaperType.exposedSchema, $schema: 'https://json-schema.org/draft/2020-12/schema' as const, type: 'object' as const, unevaluatedProperties: false as const, }, } satisfies TypeDataJson;

    const WorkflowSpecType = { ...WorkflowSpec_Schema, exposedSchema: { ...WorkflowSpec_Schema.exposedSchema, $schema: 'https://json-schema.org/draft/2020-12/schema' as const, type: 'object' as const, unevaluatedProperties: false as const, }, } satisfies TypeDataJson;

    /* const { isValid: isValidCP, errors: errorsCP } = validateType(ColoredPaperType_, { uiHints: {} });

    const { isValid: isValidWP, errors: errorsWP } = validateSampleResource(ColoredPaperType_.exposedSchema, Whitepaper);

    const { isValid: isValidYP, errors: errorsYP } = validateSampleResource(ColoredPaperType_.exposedSchema, Yellowpaper); */

    const { isValid: isValidWSFirst, errors: errorsWSFirst } = validatePrimitive(WorkflowSpecType, CONSTANTS.SCHEMA.TypeData, { uiHints: {} });

    const { isValid: isValidWSSecond, errors: errorsWSSecond } = validateResource(WorkflowSpecType.exposedSchema, WorkflowSpec_Resource);

    const [uploading, setUploading] = useState(false);
    const [uploaded, setUploaded] = useState(false);
    const [uploadError, setUploadError] = useState<string | null>(null);

    const canUpload = (isValidWSFirst || isValidWSSecond) && !uploading && !uploaded;

    async function handleUpload() {
        return; // ATTENTION: disable for now
        if (!canUpload) return;
        setUploading(true);
        setUploadError(null);
        try {
            // Generate a new ID for the resource type
            // Upload the WorkflowSpec resource type to Firestore
            // Exclude 'exposedSchema' from the metadata saved to Firestore to reduce size; it's still in Storage
            const id = await getNewId(CONSTANTS.PRIMITIVES.types);
            const res = (await uploadPrimitive(CONSTANTS.PRIMITIVES.types, { ...WorkflowSpecType, id }, ['exposedSchema'])) as UploadResult;
            if (res?.ok) {
                const { path, docId } = res;
                console.log(`Uploaded resource type to ${path} with ID ${docId}`);
                setUploaded(true);
            } else {
                console.error('Error uploading resource type:', res);
                setUploadError(typeof res === 'object' ? JSON.stringify(res) : String(res));
            }
        } catch (error: unknown) {
            console.error('Error uploading resource type:', error);
            setUploadError(error instanceof Error ? error?.message : String(error));
        } finally {
            setUploading(false);
        }
    }

    return (
        <div>
            {/* <div>
                <pre>{JSON.stringify({ isValidCP, errorsCP }, null, 2)}</pre>
            </div>
            <div>
                <pre>{JSON.stringify({ isValidWP, errorsWP }, null, 2)}</pre>
            </div>
            <div>
                <pre>{JSON.stringify({ isValidYP, errorsYP }, null, 2)}</pre>
            </div> */}
            <div>
                <pre>{JSON.stringify({ isValidWSFirst, errorsWSFirst }, null, 2)}</pre>
            </div>
            <div>
                <pre>{JSON.stringify({ isValidWSSecond, errorsWSSecond }, null, 2)}</pre>
            </div>
            <div style={{ marginTop: 12 }}>
                <button onClick={handleUpload} disabled={!canUpload} style={{ padding: '6px 12px', border: '1px solid #ccc', borderRadius: 6, cursor: canUpload ? 'pointer' : 'not-allowed', background: uploaded ? '#e6ffed' : '#fff' }}>
                    {uploaded ? 'Uploaded ✓' : uploading ? 'Uploading…' : 'Upload WorkflowSpec resource type'}
                </button>
                {uploadError && (
                    <div style={{ color: 'crimson', marginTop: 8 }}>Upload failed: {uploadError}</div>
                )}
            </div>
        </div>
    );
}