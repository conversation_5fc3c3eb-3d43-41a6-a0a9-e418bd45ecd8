'use client';

import { useState } from 'react';
import RoadMap from '../coloredPapers/RoadMap.json';

type DevMap = Record<string, { name: string }>;
type Assignment = { description: string; developers: string[]; hints: Record<string, unknown>; isActive?: boolean };
type AssignmentMap = Record<string, Assignment>;

type TaskTree = Record<string, { tasks?: TaskTree }>;

type RoadMapType = {
    developers: DevMap;
    developerAssignments: AssignmentMap;
    tasks: TaskTree;
};

// Utilities
const pathJoin = (a: string, b: string) => (a ? `${a}.${b}` : b);

type ColorVariant = 'blue' | 'red' | 'green' | 'slate';

const colorForId = (id: string): ColorVariant => {
    if (id === 'dev_1') return 'blue';
    if (id === 'dev_2') return 'red';
    if (id === 'dev_0') return 'green';
    return 'slate';
};

const Assignees: React.FC<{ ids: string[]; developers: DevMap }> = ({ ids, developers }) => {
    if (!ids || ids.length === 0) return null;
    const base = 'inline-flex items-center rounded-md px-2 py-0.5 text-xs font-medium border';
    const byColor: Record<ColorVariant, string> = {
        slate: 'bg-slate-100 dark:bg-neutral-800 text-slate-900 dark:text-slate-100 border-slate-200 dark:border-neutral-700',
        blue: 'bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-700',
        red: 'bg-rose-50 dark:bg-rose-900/30 text-rose-800 dark:text-rose-200 border-rose-200 dark:border-rose-700',
        green: 'bg-emerald-50 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-200 border-emerald-200 dark:border-emerald-700',
    };
    return (
        <div className="flex flex-wrap gap-1 mt-1">
            {ids.map((id) => {
                const label = id === 'all_devs' ? 'All devs' : developers[id]?.name || id;
                const color = id === 'all_devs' ? 'slate' : colorForId(id);
                return <span key={id} className={`${base} ${byColor[color]}`} title={id}>{label}</span>;
            })}
        </div>
    );
};

// Determine if the current node or any descendant has an active assignment
const hasActiveInSubtree = (
    fullPath: string,
    node: { tasks?: TaskTree },
    assignments: AssignmentMap
): boolean => {
    if (assignments[fullPath]?.isActive === true) return true;
    const children = node.tasks || {};
    for (const childName of Object.keys(children)) {
        const childPath = pathJoin(fullPath, childName);
        if (hasActiveInSubtree(childPath, children[childName], assignments)) return true;
    }
    return false;
};

const Node: React.FC<{
    name: string;
    node: { tasks?: TaskTree };
    fullPath: string;
    assignments: AssignmentMap;
    developers: DevMap;
    depth?: number;
}> = ({ name, node, fullPath, assignments, developers, depth = 1 }) => {
    const assignment = assignments[fullPath];
    const assigned = assignment?.developers || [];
    const hasChildren = node.tasks && Object.keys(node.tasks).length > 0;
    const HeadingTag = (['h3', 'h4', 'h5', 'h6'] as const)[Math.min(depth, 3)];

    // Default expanded if this subtree has any active assignment; otherwise collapsed
    const [expanded, setExpanded] = useState(() => hasActiveInSubtree(fullPath, node, assignments));

    return (
        <div className="mt-3">
            <div className="flex items-center gap-2">
                {hasChildren && (
                    <button
                        type="button"
                        aria-label={expanded ? 'Collapse subtree' : 'Expand subtree'}
                        aria-expanded={expanded}
                        onClick={() => setExpanded((e) => !e)}
                        className="inline-flex h-5 w-5 items-center justify-center rounded hover:bg-slate-100 dark:hover:bg-neutral-800 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <svg
                            className={`h-3.5 w-3.5 transition-transform ${expanded ? 'rotate-90' : ''}`}
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden
                        >
                            <path fillRule="evenodd" d="M7 5l6 5-6 5V5z" clipRule="evenodd" />
                        </svg>
                    </button>
                )}
                <HeadingTag className="font-semibold text-black dark:text-white">{name}</HeadingTag>
            </div>
            {assignment?.description && assignment.description.length > 0 && (
                <p className="mt-1 text-sm text-slate-700 dark:text-slate-300">{assignment.description}</p>
            )}
            <Assignees ids={assigned} developers={developers} />
            {hasChildren && expanded && (
                <div className="mt-2 ml-2 border-l border-slate-200 dark:border-neutral-700 pl-3">
                    {Object.entries(node.tasks!).map(([childName, childNode]) => (
                        <Node
                            key={childName}
                            name={childName}
                            node={childNode}
                            fullPath={pathJoin(fullPath, childName)}
                            assignments={assignments}
                            developers={developers}
                            depth={Math.min(depth + 1, 4)}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export default function RoadMapView() {
    const roadmap = RoadMap as RoadMapType;
    const { developers, developerAssignments, tasks } = roadmap;

    // Build a merged tree that includes nodes present only in assignments
    const cloneTree = (tree: TaskTree): TaskTree => {
        const out: TaskTree = {};
        for (const [k, v] of Object.entries(tree || {})) {
            out[k] = v?.tasks ? { tasks: cloneTree(v.tasks) } : {};
        }
        return out;
    };

    const ensurePath = (tree: TaskTree, path: string) => {
        if (!path) return;
        const parts = path.split('.');
        let curr: TaskTree = tree;
        for (const part of parts) {
            if (!curr[part]) curr[part] = {};
            if (!curr[part].tasks) curr[part].tasks = {};
            curr = curr[part].tasks!;
        }
    };

    const mergedTasks: TaskTree = cloneTree(tasks);
    for (const p of Object.keys(developerAssignments)) {
        ensurePath(mergedTasks, p);
    }

    return (
        <article className="mx-auto max-w-4xl px-4 py-8 bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-slate-200 dark:border-neutral-800 text-black dark:text-white">
            <h1 className="text-3xl font-bold mb-6">Roadmap</h1>
            <div>
                {Object.entries(mergedTasks).map(([rootName, rootNode]) => (
                    <Node
                        key={rootName}
                        name={rootName}
                        node={rootNode}
                        fullPath={rootName}
                        assignments={developerAssignments}
                        developers={developers}
                        depth={1}
                    />
                ))}
            </div>
        </article>
    );
}
