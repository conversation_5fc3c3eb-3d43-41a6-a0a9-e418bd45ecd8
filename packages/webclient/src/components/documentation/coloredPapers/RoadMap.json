{"developers": {"dev_0": {"name": "<PERSON>"}, "dev_1": {"name": "<PERSON><PERSON>"}, "dev_2": {"name": "<PERSON><PERSON><PERSON>"}}, "developerAssignments": {"core._lib": {"description": "", "developers": ["dev_0"], "hints": {}, "isActive": false}, "core._schemas": {"description": "", "developers": ["dev_0"], "hints": {}, "isActive": false}, "core._validator": {"description": "", "developers": ["dev_0"], "hints": {}, "isActive": false}, "core.engine": {"description": "", "developers": ["dev_0", "dev_1"], "hints": {}, "isActive": false}, "core.webclient": {"description": "", "developers": ["dev_0", "dev_2"], "hints": {}, "isActive": false}, "core.webclient.builders.primitives": {"description": "Hi Kanika! Your first task is to improve the standard (non-XR) builders for the four primitives: Format, Type, Role, and Job. As of now, the form elements for the properties of each primitive are hardcoded. However, as the schemas for the primitives might change, their UIs should be fully generated from their corresponding JSON Schemas (see _schemas folder in core/packages). If a schema property is a boolean, such as 'isPredicate' for Job, it should be rendered as a checkbox. For some properties, how to render the form element is not clear. For example, for typeId in Role, it's not clear that it should be a dropdown and not just a text input. In these cases, we'll rely on custom keywords in the JSON Schema that indicate how to render the form element. I haven't implemented this yet, we must agree on a protocol. You can start by making yourself familiar with the builders code and beautifying and modularizing the components. Do you think we'll need Figma files for this? If so, are you capable of making them, or should I ask <PERSON><PERSON><PERSON> to assign a designer? By the way, as an overall theme for ToolProof, I want the color palette of University of Minnesota (Maroon: #7A0019 and Gold: #FFCC33). NB: please create your own branch for your work. Call it branch_kanika.", "developers": ["dev_0", "dev_2"], "hints": {}, "isActive": true}, "core.webclient.builders.workflow": {"description": "", "developers": ["dev_0", "dev_2"], "hints": {}, "isActive": false}, "core.webclient.xr": {"description": "", "developers": ["dev_0", "dev_2"], "hints": {}, "isActive": false}, "core.webclient.documentation": {"description": "", "developers": ["dev_0", "dev_2"], "hints": {}, "isActive": false}, "core.webclient.chat": {"description": "", "developers": ["dev_0", "dev_2"], "hints": {}, "isActive": false}, "community._sdk": {"description": "", "developers": ["dev_0"], "hints": {}, "isActive": false}, "community.jobs": {"description": "", "developers": ["dev_0"], "hints": {}, "isActive": false}, "configuration": {"description": "", "developers": ["dev_0", "dev_1"], "hints": {}, "isActive": false}, "configuration.ContentAddressableFileStorage": {"description": "Hi again, <PERSON><PERSON>! Your first task is to implement a Content Addressable File Storage (CAFS) for Resources, which are the instances of Types, preferably within the Google Cloud Platform ecosystem. As of now, the only Resources in the system are of Type 'integer' and they are stored in the Cloud Storage bucket 'TP-resources' with file names indicating their content (e.g. '0.json', '1.json', '2.json', etc.), and a corresponding metadata document is stored in Firestore. Instead, when created each Resource should generate a cryptographic hash of its content (e.g. using SHA-256) and use that as its file name. The corresponding metadata document in Firestore should point to this hash (or we might use LangGraph Platforms build-in Postgres database for this as it already stores the GraphState during execution and it can persist this across runs). If a JobStep generates a Resource with content that already exists in the CAFS, it should not create a duplicate entry but rather reference the existing one. Take note that there's some duplicity in that we also have a ResourceId (look up its schema in core/packages/_schemas), which is a unique identifier for each Resource. I haven't quite figured out whether this is needed since we will have the hash which is also unique. However, the ResourceId is useful in that it transitively (through RoleId) encodes the Type, as well as the JobStep (and transitively the Job) that first created the Resource. You can start by making yourself familiar with how Resources are created by Jobs and try to figure out how to integrate the CAFS into this process. NB: please create your own branch for your work. Call it branch_ronak.", "developers": ["dev_0", "dev_1"], "hints": {}, "isActive": true}, "coordination": {"description": "", "developers": ["dev_0"], "hints": {}, "isActive": false}, "control": {"description": "", "developers": ["LinuxFoundation"], "hints": {}, "isActive": false}}}