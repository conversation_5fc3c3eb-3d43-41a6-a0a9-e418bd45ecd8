{"clarifications": {"description": ["JSON and JSON Schema, which form the basis of ToolProof's type system, might seem straightforward at first glance, but there are some nuances and subtleties that are important to understand. In this section, we clarify some of these points to ensure a clear understanding of how ToolProof utilizes JSON and JSON Schema."], "sections": {"1": {"description": ["In the following, we'll be using Format as an example, but the same applies to any schema-instance pair.", "Whenever we say Format (in singular and not preceded by an article), we are referring to the schema Format, as defined in core/packages/_schemas/src/schemas/primitives/format.", "Whenever we say 'a Format', 'the Format', 'this Format', or 'Formats' (in plural), we are referring to an instance of the schema Format, i.e. a specific Format."]}}}}