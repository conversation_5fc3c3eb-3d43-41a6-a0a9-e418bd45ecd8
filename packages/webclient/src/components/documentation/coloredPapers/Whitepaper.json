{"semanticIdentity": "white", "sections": {"Definition": {"description": ["ToolProof is a set of proof-based tools to integrate human and artificial intelligence.", "The 'Tool' part of ToolProof refers to the tools (i.e. primitives and Workflows) that are used to create Resources. Resources can themselves be seen as tools outside of ToolProof.", "The 'Proof' part refers to ToolProof's implementation and philosophy. For the implementation part: the fact that ToolProof is based on strict JSON Schemas for all its Resources and Job interfaces, and that it encourages formally verifiable logic for all Job implementations. For the philosophy part: the fact that all Resources produced by ToolProof Workflows must be publicly accessible, thus providing a proof of their existence and provenance to ensure alignment."]}, "Implementation": {"description": ["ToolProof has two main components: Core and Community."], "sections": {"Core": {"description": ["ToolProof Core is a Workflow engine that runs Agent-defined Jobs to generate Resources of pre-defined Types. An Agent is either a human or an AI. A Job is a primitive operation that takes Resources as input and produces Resources as output. A Type defines the schema of a Resource.", "An example of a Resource is a 'pdb' (Protein Data Bank) file used in molecular biology. A molecular docking Job could take two 'pdb' files as input, one representing a drug candidate and the other a target protein, and produce a new 'pdb' file representing the docked complex as output. This new Resource could then be used as input to another Job, such as a molecular dynamics simulation, and ultimately produce a cure for a disease."], "sections": {"Engine": {"description": ["The Engine is responsible for executing Workflows. It does so by executing the Jobs specified by a WorkflowSpec. In addition to the Workflow itself, a WorkflowSpec also defines the actual Resources that are used as inputs to the Jobs."]}, "Webclient": {"description": ["The Webclient provides an interface for building, managing, dispatching, and visualizing primitives and Workflows/WorkflowSpecs, both manually (for humans) and through an API (for AIs).", "The Webclient includes a metaverse interface for XR visualization and interaction."]}}}, "Community": {"description": ["ToolProof Community refers, in this context, to the collection of Agent-contributed primitives, in particular the Jobs that are available for Workflows, as well as an SDK (Software Development Kit) to create and deploy new Jobs.", "ToolProof Community can also refer to the community of users and developers that contribute to the project."], "sections": {"Primitives": {"description": ["Primitives are the building blocks of ToolProof. In this Whitepaper, we mention only Types and Jobs, but there are also Formats and Roles, which you can read about in the Yellowpaper (NB: not written yet)."], "sections": {"Types": {"description": ["A Type defines the schema of a Resource. Essentially, a Type is a wrapper around an underlying Format (a file format or, preferably, a MIME type). A Type is wrapped by a Role, which can further be specified as input or output to a Job. However, for simplicity, and since we promised we wouldn't be talking about other primitives here, just think that a Type alone defines the schema of a Job input or output."]}, "Jobs": {"description": ["ToolProof's Jobs are a set of computer programs that perform specific tasks within a Workflow. They can essentially be any program as long as they define what Types they take as inputs and what Types they produce as outputs. Jobs can be contributed by anyone in the community, and there can also be AI Agent Jobs that create other Jobs. There are two main types of jobs: normal Jobs (just called jobs) and Predicate Jobs."], "sections": {"Jobs": {"description": ["A (normal) Job is any Job that produces Resources with relevance outside of the Workflow itself, ultimately outside of ToolProof, as would be the case with a Job that produces a 'pdb' file that encodes a hitherto unknown cure for a disease.", "Inputs to Jobs can generally be any Resource previously generated by the current Workflow or by an earlier Workflow. The outputs of a Job can be Resources of any Type defined in the system."]}, "Predicate-Jobs": {"description": ["A Predicate Job is special in that it evaluates whether a condition is true or false and can be used to control the flow of a Workflow (e.g., branching and loops).", "Inputs to Predicate Jobs can generally be any Resource previously generated by the current Workflow or by an earlier Workflow. However, for for-loop logic specifically, the Job will be a fixed Job (a less-than type of Job) that takes an integer Resource (an iteration-target) as an input and for each invocation evaluates whether this target is reached by comparing it to an iteration-counter controlled by the Engine. A Predicate Job always produces a single output: a boolean Resource (true or false)."]}}}}}, "SDK": {"description": ["The SDK is a set of tools to create and deploy Jobs to the ToolProof platform. It includes a command-line interface, a set of templates, and a set of APIs to interact with the system."]}}}}}, "Goals": {"description": ["The ultimate goal of ToolProof is to enable the creation of complex Workflows that can integrate human and artificial intelligence to solve problems that are currently beyond the reach of either alone. This interaction is not restricted to humans being the initiators and <PERSON><PERSON> (using AI) doing the rest. For example can a Job output a tuple of Resources where one represents a human call-to action and the other one represents a Type: the type of a Resource that the Job wants the human to input back into the system. When a human (or a group of humans) has completed the action, they upload a Resource that represents the result of the action, which can then be used as input to another Job (that the first Job has created in the meantime). In this way, humans and AIs can work together in a Workflow to achieve a common goal.", "A stretch goal, and let alone a means to the ultimate goal, is to create Artificial Super Intelligence (ASI) that can autonomously create and manage Workflows to solve even more complex problems. ToolProof's take on the race to ASI is to have special Jobs (still categorized as normal Jobs with respect to the top-level taxonomy) that train a series of AI models named 'The Kent series'. Kent is not a Large Language Model (LLM) like Chat GPT, it's a model specifically designed to create and manage Workflows within the ToolProof ecosystem. It's trained on all the Resources and primitives in the system to become increasingly proficient at creating and managing Workflows that generate new Resources. Since ToolProof's data pool is highly structured, it might not need to be as large as those fed to general-purpose LLMs. Moreover, Kent will be based on the parametric knowledge of existing models, such as GPTs, and thus not need to learn general knowledge from scratch. At the output end, Kent will leverage the structured outputs feature of some of OpenAI's models, either by filtering the output through one of these models or by internalizing the feature in its own architecture. This feature uses JSON Schema to deterministically define the structure of the output so that inference will produce Resources that complies perfectly with the specified Types. For this purpose, Kent has the advantage that it's trained by, and operates within, a platform that is highly schema-driven by design."]}, "Philosophy": {"description": ["ToolProof is an Open Source project with a transitive 'Open Resource' licence, meaning that all Resources produced by ToolProof Workflows must be publicly accessible. When stable, ToolProof will be offered to the Linux Foundation for stewardship.", "ToolProof's take on the risk-reward aspect of the race to ASI is that humanity's best bet is to develop it collectively and, if successful, constrain the usage to the common good. ToolProof's design reflects all this. First, the collective nature is inherent to the platform. Second, the problems that most people want to solve will get most attention, funding, and development resources. Third, since outputs are structured Resources rather than free-form data, Types that encode possible cures for dieseases, environmental solutions, etc. can be prioritized, while Types that encode potential risks and ethical considerations can be explicitly excluded in terms of Jobs outputting them not getting run time or even deployment. This narrowed usage pattern is why ToopProof focuses on developing ASI, aimed at solving problems for the common good, rather than AGI (Artificial General Intelligence)."]}}}