'use client';
import type { <PERSON>at<PERSON><PERSON><PERSON><PERSON>, TypeMeta<PERSON>son, SignatureMetaJson, ImplementationMetaJson } from '@toolproof/_schemas';
import type { ExplorerConfig } from './_lib/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import Explorer from '@/components/explorer/Explorer';
import { useMeta, useResourcesMerged2 } from '@/_lib/client/firebaseWebHelpers';
import { useEffect, useRef } from 'react';


export default function ExplorerDataFetcher(explorerConfig: ExplorerConfig) {
    const containerRef = useRef<HTMLDivElement>(null);
    const explorerRef = useRef<Explorer | null>(null);

    // DOC: Fetch data for primitives and resources
    const { items: formatsMeta, loading: formatsLoading, error: formatsError } = useMeta<FormatMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.formats, true);
    const { items: typesMeta, loading: typesLoading, error: typesError } = useMeta<TypeMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.types, true);
    const { items: signaturesMeta, loading: signaturesLoading, error: signaturesError } = useMeta<SignatureMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.signatures, true);
    const { items: implementationsMeta, loading: implementationsLoading, error: implementationsError } = useMeta<ImplementationMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.implementations, true);
    const { items: resourcesMerged, loading: resourcesLoading, error: resourcesError } = useResourcesMerged2(CONSTANTS.NON_PRIMITIVES.resources, [CONSTANTS.SPECIALS.TYPE_WorkflowSpec, 'TYPE-jABNBKqZBQ45wB5Q5iFo', 'TYPE-wSo0cBZk3yK9F5DUb9zV'], CONSTANTS.STORAGE.tp_resources, { debug: false }); // ATTENTION: hardcoded type IDs for now

    // console.log('formatsMeta:', JSON.stringify(formatsMeta, null, 2));
    // console.log('typesMeta:', JSON.stringify(typesMeta, null, 2));
    // console.log('signaturesMeta:', JSON.stringify(signaturesMeta, null, 2));
    // console.log('implementationsMeta:', JSON.stringify(implementationsMeta, null, 2));
    // console.log('resourcesMerged:', JSON.stringify(resourcesMerged, null, 2));

    // DOC: Create and init the Explorer once when the container is ready
    useEffect(() => {
        if (!containerRef.current || explorerRef.current) return;
        const explorer = new Explorer(containerRef.current, explorerConfig);
        explorerRef.current = explorer;
        explorer.init(); // DOC: Fire-and-forget; internal start handles render loop

        return () => {
            try { explorerRef.current?.dispose(); } finally { explorerRef.current = null; }
        };
    }, [explorerConfig]);

    // DOC: Update primitives and resources data in the Explorer when loaded
    useEffect(() => {
        if (!explorerRef.current) return;
        if (formatsLoading || typesLoading || signaturesLoading || implementationsLoading || resourcesLoading) return;
        if (formatsError || typesError || signaturesError || implementationsError || resourcesError) return;
        explorerRef.current.updateData(formatsMeta, typesMeta, signaturesMeta, implementationsMeta, resourcesMerged);
    }, [formatsMeta, typesMeta, signaturesMeta, implementationsMeta, formatsLoading, typesLoading, signaturesLoading, implementationsLoading, formatsError, typesError, signaturesError, implementationsError, resourcesLoading, resourcesError, resourcesMerged]);

    return (
        <div
            ref={containerRef}
            style={{ width: '100vw', height: '100vh', backgroundColor: 'orange' }}
        ></div>
    );

}
