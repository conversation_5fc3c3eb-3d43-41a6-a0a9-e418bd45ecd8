import type { <PERSON>at<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SignatureMetaJson } from '@toolproof/_schemas';
import type { ResourceMerged } from '@/types';
import type { Interactor } from './interactors/Interactor';
import type Explorer from '../Explorer';
import * as THREE from 'three';


export interface ExplorerConfig {
    speedMultiplier: number;
    skyColor: string;
    laserColor: string;
    predicate: (obj: THREE.Object3D) => boolean;
    isGrabbable: boolean;
    recursiveRaycast: boolean; // Flag to enable recursive raycasting
    selector: Selector;
    interactorFactory: (explorer: Explorer, selector: Selector) => Interactor;
    dummyConfig: DummyConfig;
    // Controls special type-ring drawing behavior.
    // If `specialTypeRing.isSpecialEnabled` is true, the special hemisphere layout (inner/outer)
    // is applied to every type ring. The `innerHemisphere` flag chooses inner vs outer.
    // Regardless of `isSpecialEnabled`, the special layout is always used for the FORMAT_Test_0
    // format (to preserve legacy behavior).
    specialTypeRing: {
        isSpecialEnabled: boolean;
        innerHemisphere: boolean;
    };
    // Visualization settings for the shared external sphere used to place resources/implementations
    externalSphere?: {
        // Added to a computed framework base radius to place the sphere surface
        radiusOffset: number;
        // Opacity for the translucent sphere surface
        opacity: number;
    };
    // Styling for connection lines to external items (resources/implementations)
    links?: {
        externalConnections?: {
            // Visual thinness: attempted via LineBasicMaterial.linewidth (note: may be ignored on many platforms)
            linewidth: number;
            // Line opacity
            opacity: number;
        };
        // Toggles for WorkflowSpec central-box connection lines
        workflowSpecConnections?: {
            toResources: boolean;
            toImplementations: boolean;
            toSignatures: boolean;
            toTypes: boolean;
            // Styling overrides for WS lines (prefer these; fall back to externalConnections)
            opacity?: number;
            lineWidth?: number; // alias for linewidth
            linewidth?: number;
        };
    };
}

export interface DummyConfig {
    dummiesEnabled: boolean;
    dummyIndicatorPoleHeight: number;
    dummyFlagColor: THREE.Color;
    nonDummyFlagColor: THREE.Color;
    showAllSignatureRoles: boolean;
    externalBounds?: {
        resourcesPerDummyType: { min: number; max: number };
        resourcesPerDummySignatureRoleType: { min: number; max: number };
        implementationsPerDummySignature: { min: number; max: number };
    };
}

export interface SelectionCommand {
    selectedObject: THREE.Object3D | null;
    restoreOriginalPosition?: boolean;
}

export interface Selector {
    onSelectStart(intersectedObject: THREE.Object3D | null): SelectionCommand;
    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand;
}

export interface PrimitiveMeshMap {
    formats: (FormatMetaJson & { mesh: THREE.Mesh; })[];
    types: (TypeMetaJson & { mesh: THREE.Mesh; })[];
    roles: (RoleInSignature & { mesh: THREE.Mesh; })[];
    signatures: (SignatureMetaJson & { mesh: THREE.Mesh; })[];
}

export interface ResourceMeshMap {
    [typeId: string]: (ResourceMerged & { mesh: THREE.Mesh; })[];
}

export interface GeometryConfig {
    kind: 'box' | 'sphere';
    widthSegments: number;
    heightSegments: number;
}

export interface SphereConfig extends GeometryConfig {
    kind: 'sphere';
    ringRadius: number;
    radius: number;
}

export interface BoxConfig extends GeometryConfig {
    kind: 'box';
    width: number;
    height: number;
    depth: number;
    widthSegments: number;
    heightSegments: number;
    depthSegments: number;
}

export interface MaterialConfig {
    color: THREE.Color;
    metalness: number;
    roughness: number;
    emissive: THREE.Color
}

export interface MeshConfig<T extends GeometryConfig> {
    geometry: T;
    material: MaterialConfig;
}

export type NormalDirection = 'x' | 'y' | 'z';

export type OrientationMode = 'given' | 'radial' | 'tangent';

// Minimal role representation used at runtime: roles are scoped to signatures
// and reference a target typeId, with an input/output direction.
export interface RoleInSignature {
    id: string; // RoleId
    name?: string;
    typeId?: string; // associated TypeId (optional here; available at construction time)
    direction?: 'input' | 'output';
}
