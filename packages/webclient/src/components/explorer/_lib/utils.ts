import type { FormatMeta<PERSON>son, TypeMeta<PERSON>son, SignatureMetaJson, ImplementationMetaJson } from '@toolproof/_schemas';
import type { ResourceMerged } from '@/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { v4 as uuidv4 } from 'uuid';
import * as THREE from 'three';

// Generic augmentation: clone-like dummies to reach totalCount.
export type DummyLinkOptions<T> = {
    // Candidate IDs from the related collection to link to (e.g., dummy formats/types/roles)
    linkIds?: string[];
    // Given the dummy index and candidate linkIds, return a shallow patch to merge into the dummy
    // For example: { formatId } or { typeId } or { resources: { inputs: [...], outputs: [...] } }
    getLinkPatch?: (idx: number, linkIds: string[]) => Partial<T>;
};

// Generic augmentation: clone-like dummies to reach totalCount.
export const augmentWithDummies = <T extends { id: string }>(items: T[], totalCount: number, cloneLike?: (example: T, idx: number) => T, linkOptions?: DummyLinkOptions<T>): T[] => {
    const result = [...items];
    const needed = Math.max(totalCount - result.length, 0);
    if (needed === 0) return result;

    const example = items[0];

    // If we have no example at all, synthesize minimal dummies so visuals still render.
    if (!example) {
        for (let i = 0; i < needed; i++) {
            const dummy = { id: uuidv4() } as T;
            (dummy as unknown as Record<string, unknown>)['name'] = 'Dummy';
            (dummy as unknown as Record<string, unknown>)['description'] = '';
            const ids = linkOptions?.linkIds ?? [];
            if (ids.length && linkOptions?.getLinkPatch) {
                const patch = linkOptions.getLinkPatch(i, ids);
                Object.assign(dummy as unknown as Record<string, unknown>, patch as Record<string, unknown>);
            }
            result.push(dummy);
        }
        return result;
    }

    for (let i = 0; i < needed; i++) {
        let dummy: T;
        if (cloneLike) {
            dummy = cloneLike(example, i);
        } else {
            // Best-effort shallow clone preserving required fields; adjust common labels if present
            dummy = { ...(example as unknown as Record<string, unknown>) } as T;
            (dummy as unknown as Record<string, unknown>)['id'] = uuidv4();
            if ('name' in example) (dummy as unknown as Record<string, unknown>)['name'] = 'Dummy';
            if ('description' in example) (dummy as unknown as Record<string, unknown>)['description'] = 'Dummy';
        }

        // Optionally link this dummy to related dummy IDs (formats/types/roles) for realism
        const ids = linkOptions?.linkIds ?? [];
        if (ids.length && linkOptions?.getLinkPatch) {
            const patch = linkOptions.getLinkPatch(i, ids);
            Object.assign(dummy as unknown as Record<string, unknown>, patch as Record<string, unknown>);
        }
        result.push(dummy);
    }
    return result;
};

// High-level helper: add realistic dummies across formats, types, roles, signatures with random assignments and constraints
export function addRealisticDummies(params: {
    formats: FormatMetaJson[];
    types: TypeMetaJson[];
    signatures: SignatureMetaJson[];
    counts?: { formats?: number; types?: number; roles?: number; signatures?: number };
}): {
    formats: FormatMetaJson[];
    types: TypeMetaJson[];
    signatures: SignatureMetaJson[];
} {
    const { formats, types, signatures } = params;
    const counts = { formats: 10, types: 15, roles: 8, signatures: 5, ...(params.counts ?? {}) };

    // 1) Formats: base list
    const formatsMeta = augmentWithDummies<FormatMetaJson>(
        formats,
        counts.formats!,
        (ex, _i) => ({ ...ex, id: 'FORMAT-' + uuidv4(), name: 'DummyFormat', description: '' })
    );
    const dummyFormatIds = formatsMeta.filter(f => String(f.name).toLowerCase().includes('dummy')).map(f => f.id);

    // 2) Types: each dummy gets a random dummy formatId
    let typesMeta = augmentWithDummies<TypeMetaJson>(
        types,
        counts.types!,
        (ex, _i) => ({ ...ex, id: 'TYPE-' + uuidv4(), name: 'DummyType', description: '' }),
        {
            linkIds: dummyFormatIds,
            getLinkPatch: (_idx, ids) => {
                const pick = ids.length ? ids[Math.floor(Math.random() * ids.length)] : undefined;
                return pick ? ({ formatId: pick } as Partial<TypeMetaJson>) : {};
            }
        }
    );
    const dummyTypeIds = typesMeta.filter(t => String(t.name).toLowerCase().includes('dummy')).map(t => t.id);

    // 3) Signatures: synthesize dummy signatures with roles mapped to dummy types
    const signaturesMeta = augmentWithDummies<SignatureMetaJson>(
        signatures,
        counts.signatures!,
        (ex, _i) => ({ ...ex, id: 'SIGNATURE-' + uuidv4(), name: 'DummySignature', description: '' }),
        {
            // Provide dummy type ids as candidates when crafting roles
            linkIds: dummyTypeIds,
            getLinkPatch: (_idx, ids) => {
                // Helper to pick K distinct type ids
                const pickDistinct = (arr: string[], k: number) => {
                    if (arr.length === 0) return [] as string[];
                    if (arr.length <= k) return [...arr];
                    const picked = new Set<string>();
                    while (picked.size < k) picked.add(arr[Math.floor(Math.random() * arr.length)]);
                    return Array.from(picked);
                };

                // Generate 1-3 input roles and 1-2 output roles
                const inCount = 1 + Math.floor(Math.random() * 3);
                const outCount = 1 + Math.floor(Math.random() * 2);
                const inTypes = pickDistinct(ids, inCount);
                const outTypes = pickDistinct(ids, outCount);
                const inputs: Record<string, string> = {};
                const outputs: Record<string, string> = {};
                for (const t of inTypes) inputs['ROLE-' + uuidv4()] = t;
                for (const t of outTypes) outputs['ROLE-' + uuidv4()] = t;
                const isPredicate = Math.random() < 0.3;
                return { isPredicate, roles: { inputs, outputs } } as unknown as Partial<SignatureMetaJson>;
            }
        }
    );

    // Extra: ensure FORMAT_Test_0 has some composing dummy types even if there are no non-dummy types for it.
    // These are added in addition to the target total count for types.
    try {
        const formatTest0Id = CONSTANTS.SPECIALS.FORMAT_Test_0;
        const hasFormatTest0 = formatsMeta.some(f => f.id === formatTest0Id);
        if (hasFormatTest0) {
            const hasNonDummyForTest0 = types.some(t => t.formatId === formatTest0Id && !String((t as unknown as { name?: unknown }).name).toLowerCase().includes('dummy'));
            if (!hasNonDummyForTest0) {
                const extraCount = 5 + Math.floor(Math.random() * 6); // 5..10
                const extras: TypeMetaJson[] = [];
                for (let i = 0; i < extraCount; i++) {
                    const extra: TypeMetaJson = {
                        ...(types[0] ?? ({} as TypeMetaJson)),
                        id: 'TYPE-' + uuidv4(),
                        name: 'DummyType',
                        description: '',
                        formatId: formatTest0Id,
                    } as TypeMetaJson;
                    extras.push(extra);
                }
                typesMeta = typesMeta.concat(extras);
            }
        }
    } catch {
        // best-effort only
    }

    return { formats: formatsMeta, types: typesMeta, signatures: signaturesMeta };
}

// Compute an orthonormal basis (u, v, n) for a plane given its normal vector n.
export function computeBasisFromNormal(normalVec: THREE.Vector3): { u: THREE.Vector3; v: THREE.Vector3; n: THREE.Vector3 } {
    const n = normalVec.clone().normalize();
    // Choose a helper up that is not parallel to n
    const up = Math.abs(n.y) > 0.9 ? new THREE.Vector3(1, 0, 0) : new THREE.Vector3(0, 1, 0);
    const u = new THREE.Vector3().crossVectors(up, n);
    if (u.lengthSq() < 1e-8) {
        // Fallback if up was accidentally parallel
        const alt = new THREE.Vector3(0, 0, 1);
        u.crossVectors(alt, n);
    }
    u.normalize();
    const v = new THREE.Vector3().crossVectors(n, u).normalize();
    return { u, v, n };
}

// Compute a basis (u, v) whose plane follows the format ring at the given format center.
// The plane normal equals the tangent of the format ring at that location (up x radial),
// making this orientation insensitive to Y offsets of child rings.
export function computeFormatFollowingBasis(formatCenter: THREE.Vector3) {
    const worldUp = new THREE.Vector3(0, 1, 0);
    const radial = new THREE.Vector3(formatCenter.x, 0, formatCenter.z);
    if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
    radial.normalize();
    const tangentNormal = new THREE.Vector3().crossVectors(worldUp, radial);
    if (tangentNormal.lengthSq() < 1e-6) tangentNormal.set(0, 0, -1);
    tangentNormal.normalize();
    const b = computeBasisFromNormal(tangentNormal);
    return { u: b.u, v: b.v, normal: tangentNormal };
}

// Synthesize dummy resources for visualization when real data is sparse.
// - For every dummy type, ensure a small number of resources exist.
// - For every dummy signature, also ensure resources exist for the types referenced by its roles.
export function augmentResourcesWithDummies(params: {
    resourcesByType: Record<string, ResourceMerged[]>;
    types: TypeMetaJson[];
    signatures: SignatureMetaJson[];
    opts?: {
        perDummyType?: { min: number; max: number };
        perSignatureRoleType?: { min: number; max: number };
    };
}): Record<string, ResourceMerged[]> {
    const { resourcesByType, types, signatures } = params;
    const perDummyType = params.opts?.perDummyType ?? { min: 2, max: 4 };
    const perSigRole = params.opts?.perSignatureRoleType ?? { min: 1, max: 3 };

    const out: Record<string, ResourceMerged[]> = { ...resourcesByType };

    // Helper to ensure at least N resources for a given type id
    const ensureForType = (typeId: string, minCount: number, maxCount: number) => {
        const existing = out[typeId] ?? [];
        const already = existing.length;
        const target = Math.max(already, minCount + Math.floor(Math.random() * (Math.max(maxCount - minCount + 1, 1))));
        if (already >= target) {
            if (!out[typeId]) out[typeId] = existing; // ensure key exists
            return;
        }
        const needed = target - already;
        const additions: ResourceMerged[] = [];
        for (let i = 0; i < needed; i++) {
            const semanticIdentity = 100 + Math.floor(Math.random() * 900);
            const idSuffix = uuidv4();
            const path = `dummy/${typeId}/res-${idSuffix}.json`;
            additions.push({
                path,
                timestamp: new Date().toISOString(),
                exposedData: { semanticIdentity },
            } as ResourceMerged);
        }
        out[typeId] = existing.concat(additions);
    };

    // 1) All dummy types
    const dummyTypeIds = types
        .filter((t) => String((t as unknown as { name?: unknown }).name).toLowerCase().includes('dummy'))
        .map((t) => t.id);
    for (const tid of dummyTypeIds) ensureForType(tid, perDummyType.min, perDummyType.max);

    // 2) Types referenced by dummy signatures' roles
    const isDummySignature = (s: SignatureMetaJson) => String((s as unknown as { name?: unknown }).name).toLowerCase().includes('dummy');
    const getRoleTypeIds = (s: SignatureMetaJson) => {
        const roles = (s as unknown as { roles?: { inputs?: Record<string, string>; outputs?: Record<string, string> } }).roles ?? {};
        const ins = Object.values(roles.inputs ?? {});
        const outs = Object.values(roles.outputs ?? {});
        return [...ins, ...outs].filter(Boolean) as string[];
    };
    for (const sig of signatures) {
        if (!isDummySignature(sig)) continue;
        const typeIds = getRoleTypeIds(sig);
        for (const tid of typeIds) ensureForType(tid, perSigRole.min, perSigRole.max);
    }

    return out;
}

// Synthesize dummy implementations for dummy signatures when missing/sparse.
// Ensures at least a random count per dummy signature.
export function augmentImplementationsWithDummies(params: {
    implementations: ImplementationMetaJson[];
    signatures: SignatureMetaJson[];
    opts?: {
        perDummySignature?: { min: number; max: number };
    };
}): ImplementationMetaJson[] {
    const { implementations, signatures } = params;
    const perSig = params.opts?.perDummySignature ?? { min: 1, max: 3 };

    const result: ImplementationMetaJson[] = [...implementations];
    const bySignature = new Map<string, ImplementationMetaJson[]>();
    for (const impl of result) {
        const sid = (impl as ImplementationMetaJson).signatureId as string | undefined;
        if (!sid) continue;
        const arr = bySignature.get(sid) ?? [];
        arr.push(impl);
        bySignature.set(sid, arr);
    }

    const isDummySignature = (s: SignatureMetaJson) => String((s as unknown as { name?: unknown }).name).toLowerCase().includes('dummy');

    for (const sig of signatures) {
        if (!isDummySignature(sig)) continue;
        const sid = sig.id;
        const existing = bySignature.get(sid) ?? [];
        const already = existing.length;
        const target = Math.max(already, perSig.min + Math.floor(Math.random() * (Math.max(perSig.max - perSig.min + 1, 1))));
        const needed = target - already;
        for (let i = 0; i < needed; i++) {
            const impl: ImplementationMetaJson = {
                id: 'IMPL-' + uuidv4(),
                name: 'DummyImplementation',
                description: '',
                signatureId: sid,
            } as ImplementationMetaJson;
            result.push(impl);
        }
    }

    return result;
}