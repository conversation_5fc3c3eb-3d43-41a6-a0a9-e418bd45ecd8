import type { OrientationMode, MeshConfig, BoxConfig, SphereConfig, ExplorerConfig } from './types';
import * as THREE from 'three';

export const orientationMode: OrientationMode = 'radial';

export const primitiveCounts = {
    formats: 10,
    types: 35,
    signatures: 5,
};

const sphereGeometryBase = {
    ringRadius: {
        value: 100,
        offsets: {
            formats: 1.0,
            types: 0.1,
            roles: 0.05,
            signatures: 0.005,
        }
    },
    radius: {
        value: 2.5,
        offsets: {
            formats: 1.0,
            types: 0.5,
            roles: 0.2,
            signatures: 0.1,
        }
    },
    widthSegments: {
        value: 100,
        offsets: {
            formats: 1.0,
            types: 0.6,
            roles: 0.3,
            signatures: 0.1,
        }
    },
    heightSegments: {
        value: 100,
        offsets: {
            formats: 1.0,
            types: 0.6,
            roles: 0.3,
            signatures: 0.1,
        }
    },
};

export const meshConfigFormats: MeshConfig<SphereConfig> = {
    geometry: {
        kind: 'sphere',
        ringRadius: sphereGeometryBase.ringRadius.value * sphereGeometryBase.ringRadius.offsets.formats,
        radius: sphereGeometryBase.radius.value * sphereGeometryBase.radius.offsets.formats,
        widthSegments: Math.floor(sphereGeometryBase.widthSegments.value * sphereGeometryBase.widthSegments.offsets.formats),
        heightSegments: Math.floor(sphereGeometryBase.heightSegments.value * sphereGeometryBase.heightSegments.offsets.formats),
    },
    material: {
        color: new THREE.Color('red'),
        metalness: 0.5,
        roughness: 0.5,
        emissive: new THREE.Color('black'),
    },
};

export const meshConfigTypes: MeshConfig<SphereConfig> = {
    geometry: {
        kind: 'sphere',
        ringRadius: sphereGeometryBase.ringRadius.value * sphereGeometryBase.ringRadius.offsets.types,
        radius: sphereGeometryBase.radius.value * sphereGeometryBase.radius.offsets.types,
        widthSegments: Math.floor(sphereGeometryBase.widthSegments.value * sphereGeometryBase.widthSegments.offsets.types),
        heightSegments: Math.floor(sphereGeometryBase.heightSegments.value * sphereGeometryBase.heightSegments.offsets.types),
    },
    material: {
        color: new THREE.Color('blue'),
        metalness: 0.5,
        roughness: 0.5,
        emissive: new THREE.Color('black'),
    },
};

export const meshConfigRoles: MeshConfig<SphereConfig> = {
    geometry: {
        kind: 'sphere',
        ringRadius: sphereGeometryBase.ringRadius.value * sphereGeometryBase.ringRadius.offsets.roles,
        radius: sphereGeometryBase.radius.value * sphereGeometryBase.radius.offsets.roles,
        widthSegments: Math.floor(sphereGeometryBase.widthSegments.value * sphereGeometryBase.widthSegments.offsets.roles),
        heightSegments: Math.floor(sphereGeometryBase.heightSegments.value * sphereGeometryBase.heightSegments.offsets.roles),
    },
    material: {
        color: new THREE.Color('violet'),
        metalness: 0.5,
        roughness: 0.5,
        emissive: new THREE.Color('black'),
    },
};

export const meshConfigSignatures: MeshConfig<BoxConfig> = {
    geometry: {
        kind: 'box',
        width: 3,
        height: 3,
        depth: 3,
        widthSegments: 1,
        heightSegments: 1,
        depthSegments: 1,
    },
    material: {
        color: new THREE.Color('gray'),
        metalness: 0.5,
        roughness: 0.5,
        emissive: new THREE.Color('black'),
    },
};

// Configuration for implementation meshes (used as material presets; geometry is built as cones at runtime)
export const meshConfigImplementations: MeshConfig<BoxConfig> = {
    geometry: {
        kind: 'box',
        width: 2,
        height: 2,
        depth: 2,
        widthSegments: 1,
        heightSegments: 1,
        depthSegments: 1,
    },
    material: {
        // Matches the inlined values used previously in drawImplementations
        color: new THREE.Color(0x66ccff),
        metalness: 0.25,
        roughness: 0.75,
        emissive: new THREE.Color('black'),
    },
};

// Base (static) Explorer config. Dynamic React-provided deps (predicate, selector, interactorFactory)
// are injected by the factory below inside the component file.
export const baseExplorerConfig: Omit<ExplorerConfig, 'predicate' | 'selector' | 'interactorFactory'> = {
    speedMultiplier: 5,
    skyColor: 'skyblue',
    laserColor: 'yellow',
    isGrabbable: false,
    recursiveRaycast: true,
    dummyConfig: {
        dummiesEnabled: true,
        dummyIndicatorPoleHeight: 1.0,
        dummyFlagColor: new THREE.Color(0x000000),
        nonDummyFlagColor: new THREE.Color(0xffffff),
        showAllSignatureRoles: false,
        externalBounds: {
            resourcesPerDummyType: { min: 20, max: 40 },
            resourcesPerDummySignatureRoleType: { min: 1, max: 3 },
            implementationsPerDummySignature: { min: 10, max: 30 },
        },
    },
    // Default special type-ring options. When `isSpecialEnabled` is true, apply the
    // special hemisphere layout to every type ring. `innerHemisphere` chooses inner vs outer.
    specialTypeRing: {
        isSpecialEnabled: true,
        innerHemisphere: false,
    },
    // Shared external sphere for resources and implementations
    externalSphere: {
        radiusOffset: 16,
        opacity: 0.2,
    },
    // Grouped styling for external connection lines (types/signatures -> resources/implementations)
    links: {
        externalConnections: {
            linewidth: 0.25,
            opacity: 0.5,
        },
        workflowSpecConnections: {
            toResources: true,
            toImplementations: true,
            toSignatures: true,
            toTypes: true,
            opacity: 1.0,
            linewidth: 0.5,
        },
    },
};

export type ExplorerDynamicDeps = Pick<ExplorerConfig, 'predicate' | 'selector' | 'interactorFactory'>;

// Factory to compose final ExplorerConfig in React-land while keeping this module React-free.
export function makeExplorerConfig(
    deps: ExplorerDynamicDeps,
    overrides?: Partial<Omit<ExplorerConfig, keyof ExplorerDynamicDeps>>
): ExplorerConfig {
    return { ...baseExplorerConfig, ...(overrides ?? {}), ...deps } as ExplorerConfig;
}
