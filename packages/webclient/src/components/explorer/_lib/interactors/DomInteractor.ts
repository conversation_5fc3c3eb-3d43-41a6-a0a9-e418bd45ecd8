import * as THREE from 'three';
import type Explorer from '../../Explorer';
import type { Selector } from '../types';
import type { Interactor } from './Interactor';
import { applyHighlighting, computeDisplayText, collectInteractiveObjects, raycastInteractive } from './shared';

export class DomInteractor implements Interactor {
    private explorer: Explorer;
    private selector: Selector;
    private _intersectedObject: THREE.Object3D | null = null;
    private _selectedObject: THREE.Object3D | null = null;
    private grabbedObjectOriginalPosition: THREE.Vector3 | null = null;
    // Mouse-based interaction (non-XR)
    private mouse: THREE.Vector2 = new THREE.Vector2();
    private mouseRaycaster: THREE.Raycaster = new THREE.Raycaster();
    private isMouseInteracting: boolean = false;
    private tooltip: HTMLDivElement | null = null;
    private removeMouseHandlers: (() => void) | null = null;

    constructor(explorer: Explorer, selector: Selector) {
        this.explorer = explorer;
        this.selector = selector;
        this.removeMouseHandlers = this.setupMouseInteractionHelper();
        this.tooltip = this.createTooltipElement();
    }

    dispose(): void {
        if (this.removeMouseHandlers) {
            try { this.removeMouseHandlers(); } finally { this.removeMouseHandlers = null; }
        }
        this.cleanupTooltipHelper();
    }

    // Expose current state to composition users via readonly accessors
    get intersectedObject(): THREE.Object3D | null { return this._intersectedObject; }
    get selectedObject(): THREE.Object3D | null { return this._selectedObject; }

    updateMovement(_delta: number): void {
        // Non-XR: no movement handled here
    }

    updateInteraction(): void {
        let intersectedObject: THREE.Object3D | null = null;
        if (this.isMouseInteracting) intersectedObject = this.performMouseRaycast();
        this._intersectedObject = intersectedObject;
        applyHighlighting(this.explorer, this._intersectedObject);
        const objectToDisplay = this._selectedObject || this._intersectedObject;
        const text = computeDisplayText(this.explorer, objectToDisplay);
        this.showTooltipHelper(text);
    }

    raycastFromController(): THREE.Object3D | null {
        // In non-XR, we can reuse the mouse raycast, though controller events won't fire.
        return this.performMouseRaycast();
    }

    private createTooltipElement = (): HTMLDivElement => {
        const tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            pointer-events: none;
            z-index: 1000;
            max-width: 300px;
            white-space: pre-wrap;
            border: 1px solid #333;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            display: none;
        `;
        document.body.appendChild(tooltip);
        return tooltip;
    };

    private showTooltipHelper(text: string) {
        if (!this.tooltip) return;
        if (!text || text.trim() === '') {
            this.tooltip.style.display = 'none';
            return;
        }
        this.tooltip.textContent = text;
        this.tooltip.style.display = 'block';
    }

    private cleanupTooltipHelper() {
        if (this.tooltip && this.tooltip.parentNode) {
            this.tooltip.parentNode.removeChild(this.tooltip);
            this.tooltip = null;
        }
    }

    private updateMousePositionHelper(event: MouseEvent) {
        const canvas = this.explorer.renderer.domElement;
        const rect = canvas.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    private performMouseRaycast(): THREE.Object3D | null {
        const allSpheres: THREE.Object3D[] = collectInteractiveObjects(this.explorer, { applyPredicate: true });
        if (allSpheres.length === 0) return null;
        this.mouseRaycaster.setFromCamera(this.mouse, this.explorer.camera);
        const intersects = raycastInteractive(this.explorer, this.mouseRaycaster, allSpheres);
        return intersects.length > 0 ? intersects[0].object : null;
    }

    private clearHighlightingHelper() { applyHighlighting(this.explorer, null); }

    private setupMouseInteractionHelper() {
        const canvas = this.explorer.renderer.domElement;

        const onMouseMove = (event: MouseEvent) => {
            this.updateMousePositionHelper(event as MouseEvent);
            this.isMouseInteracting = true;
            if (this.tooltip) {
                this.tooltip.style.left = (event.clientX + 10) + 'px';
                this.tooltip.style.top = (event.clientY - 10) + 'px';
            }
        };

        const onClick = (event: MouseEvent) => {
            this.updateMousePositionHelper(event as MouseEvent);
            const intersectedObject = this.performMouseRaycast();
            if (intersectedObject) this._selectedObject = intersectedObject;
            else this._selectedObject = null;
        };

        const onMouseLeave = () => {
            this.isMouseInteracting = false;
            this.clearHighlightingHelper();
            if (this.tooltip) this.tooltip.style.display = 'none';
        };

        canvas.addEventListener('mousemove', onMouseMove);
        canvas.addEventListener('click', onClick);
        canvas.addEventListener('mouseleave', onMouseLeave);

        return () => {
            canvas.removeEventListener('mousemove', onMouseMove);
            canvas.removeEventListener('click', onClick);
            canvas.removeEventListener('mouseleave', onMouseLeave);
        };
    }
}
