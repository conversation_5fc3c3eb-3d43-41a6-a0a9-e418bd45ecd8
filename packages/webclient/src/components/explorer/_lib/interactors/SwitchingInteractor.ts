import type * as THREE from 'three';
import type Explorer from '../../Explorer';
import type { Selector } from '../types';
import type { Interactor } from './Interactor';
import { DomInteractor } from './DomInteractor';
import { XrInteractor } from './XrInteractor';
import { VRButton } from 'three/examples/jsm/webxr/VRButton.js';

type XRManagerLike = {
    addEventListener: (type: string, listener: () => void) => void;
    removeEventListener: (type: string, listener: () => void) => void;
    getSession: () => XRSession | null;
};

// A thin delegating interactor that switches between DOM and XR implementations
// based on WebXR session lifecycle, keeping Explorer unaware of mode specifics.
export class SwitchingInteractor implements Interactor {
    private explorer: Explorer;
    private selector: Selector;
    private dom: DomInteractor;
    private xr: XrInteractor | null = null;
    private onSessionStart?: () => void;
    private onSessionEnd?: () => void;
    private xrManager?: XRManagerLike;
    private vrButtonElement: HTMLElement | null = null;

    constructor(explorer: Explorer, selector: Selector) {
        this.explorer = explorer;
        this.selector = selector;

        // Start in non-XR mode
        this.dom = new DomInteractor(explorer, selector);

        // Ensure WebXR is enabled and present an entry button before any session exists
        try {
            // Enable XR on the renderer (safe to do once)
            (this.explorer.renderer.xr as unknown as { enabled: boolean }).enabled = true;
            // Always bind a fresh VRButton to THIS renderer; replace any existing one
            if (typeof document !== 'undefined') {
                const existing = document.getElementById('VRButton');
                if (existing && existing.parentElement) existing.parentElement.removeChild(existing);
                const btn = VRButton.createButton(this.explorer.renderer);
                document.body.appendChild(btn);
                this.vrButtonElement = btn;
            }
        } catch { /* ignore if not supported or SSR */ }

        // Listen to XR session lifecycle to lazily switch
        const xrManager = this.explorer.renderer.xr as unknown as XRManagerLike;
        this.xrManager = xrManager;

        this.onSessionStart = () => {
            // Initialize XR interactor when entering XR
            if (!this.xr) this.xr = new XrInteractor(this.explorer, this.selector);
        };
        this.onSessionEnd = () => {
            // Dispose XR-specific UI when exiting XR
            if (this.xr) {
                this.xr.dispose();
                // Keep instance to avoid re-creating resources too often; or set to null
                // this.xr = null;
            }
        };

        try {
            xrManager.addEventListener('sessionstart', this.onSessionStart!);
            xrManager.addEventListener('sessionend', this.onSessionEnd!);
        } catch { /* ignore if not supported */ }
    }

    private current(): Interactor {
        // Use XR interactor when a session is active
        const hasSession = !!this.explorer.renderer.xr.getSession();
        if (hasSession) {
            if (!this.xr) this.xr = new XrInteractor(this.explorer, this.selector);
            return this.xr;
        }
        return this.dom;
    }

    // Expose current interactor state through readonly accessors
    get intersectedObject(): THREE.Object3D | null { return this.current().intersectedObject; }
    get selectedObject(): THREE.Object3D | null { return this.current().selectedObject; }

    updateInteraction(): void {
        this.current().updateInteraction();
    }

    updateMovement(delta: number): void {
        this.current().updateMovement(delta);
    }

    raycastFromController(): THREE.Object3D | null {
        return this.current().raycastFromController();
    }

    dispose(): void {
        if (this.onSessionStart && this.xrManager) {
            try { this.xrManager.removeEventListener('sessionstart', this.onSessionStart); } catch { }
        }
        if (this.onSessionEnd && this.xrManager) {
            try { this.xrManager.removeEventListener('sessionend', this.onSessionEnd); } catch { }
        }
        try {
            // Remove VR button if we added or adopted one
            if (this.vrButtonElement && this.vrButtonElement.parentElement) {
                this.vrButtonElement.parentElement.removeChild(this.vrButtonElement);
            }
            this.vrButtonElement = null;
        } catch { }
        this.dom.dispose();
        if (this.xr) this.xr.dispose();
    }
}
