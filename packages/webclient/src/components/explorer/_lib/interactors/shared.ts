import * as THREE from 'three';
import { CONSTANTS } from '@toolproof/_lib/constants';
import type Explorer from '../../Explorer';

// Collects all interactive objects in the scene. These are objects that carry
// one of our known entity markers in userData. We typically store this on
// the Mesh itself.
export function collectInteractiveObjects(
    explorer: Explorer,
    options?: { applyPredicate?: boolean }
): THREE.Object3D[] {
    const res: THREE.Object3D[] = [];
    explorer.scene.traverse((child) => {
        const ent = child.userData?.entity;
        if (
            ent === CONSTANTS.PRIMITIVES.formats ||
            ent === CONSTANTS.PRIMITIVES.types ||
            ent === CONSTANTS.PRIMITIVES.roles ||
            ent === CONSTANTS.PRIMITIVES.signatures ||
            ent === CONSTANTS.PRIMITIVES.implementations ||
            ent === CONSTANTS.NON_PRIMITIVES.resources
        ) {
            res.push(child);
        }
    });
    const applyPred = options?.applyPredicate ?? true;
    if (applyPred && typeof explorer.config?.predicate === 'function') {
        return res.filter((obj) => explorer.config.predicate(obj));
    }
    return res;
}

// Apply emissive highlight to interactive meshes based on the current target
export function applyHighlighting(explorer: Explorer, highlightTarget: THREE.Object3D | null): void {
    const setForMaterial = (mat: THREE.Material, highlighted: boolean) => {
        // Ensure userData exists for storing originals (Material.userData is provided by three typings)
        const ud = (mat.userData ??= {}) as { __origEmissive?: number; __origColor?: number };

        // If material supports emissive, prefer that
        if ('emissive' in mat && mat.emissive instanceof THREE.Color) {
            if (ud.__origEmissive === undefined) {
                ud.__origEmissive = mat.emissive.getHex();
            }
            mat.emissive.set(highlighted ? 0xffff00 : (ud.__origEmissive ?? 0x000000));
            return;
        }
        // Fallback: toggle base color when available
        if ('color' in mat && mat.color instanceof THREE.Color) {
            if (ud.__origColor === undefined) {
                ud.__origColor = mat.color.getHex();
            }
            mat.color.set(highlighted ? 0xffff00 : (ud.__origColor ?? 0xffffff));
        }
    };

    explorer.scene.traverse((child) => {
        const isInteractive = (
            child.userData?.entity === CONSTANTS.PRIMITIVES.formats ||
            child.userData?.entity === CONSTANTS.PRIMITIVES.types ||
            child.userData?.entity === CONSTANTS.PRIMITIVES.roles ||
            child.userData?.entity === CONSTANTS.PRIMITIVES.signatures ||
            child.userData?.entity === CONSTANTS.PRIMITIVES.implementations ||
            child.userData?.entity === CONSTANTS.NON_PRIMITIVES.resources
        );
        if (isInteractive && child instanceof THREE.Mesh) {
            const highlighted = highlightTarget === child;
            const m = child.material;
            if (Array.isArray(m)) {
                m.forEach((mm) => setForMaterial(mm, highlighted));
            } else if (m) {
                setForMaterial(m, highlighted);
            }
        }
    });
}

// Compute label/tooltip text for a given object based on primitive metadata and userData
export function computeDisplayText(explorer: Explorer, objectToDisplay: THREE.Object3D | null): string {
    if (!objectToDisplay) return '';

    // Search primitive maps
    const flattened = Object.values(explorer.primitiveMeshMap).flatMap((arr) => arr);
    for (const dataObject of flattened) {
        if (dataObject.mesh === objectToDisplay) {
            return dataObject.description
                ? `${dataObject.name}:\n${dataObject.description}`
                : (dataObject.name ? `${dataObject.name}\n` : `${dataObject.id}\n`);
        }
    }

    // Fallback to userData
    const ud = objectToDisplay.userData ?? {};
    if (ud.name != null && ud.description != null) {
        return ud.description ? `${ud.name}:\n${ud.description}` : `${ud.name}\n`;
    }
    if (ud.name != null) {
        return `${ud.name}\n`;
    }
    return '';
}

// Perform a raycast against interactive objects, honoring ExplorerConfig.recursiveRaycast.
// Optionally pass a pre-collected object list; otherwise it will collect with predicate.
export function raycastInteractive(
    explorer: Explorer,
    raycaster: THREE.Raycaster,
    objects?: THREE.Object3D[],
): THREE.Intersection[] {
    const targets = objects ?? collectInteractiveObjects(explorer, { applyPredicate: true });
    const recursive = explorer.config.recursiveRaycast ?? false;
    return raycaster.intersectObjects(targets, recursive);
}
