import type Explorer from '../../Explorer';
import type { Selector } from '../types';
import type { Interactor } from './Interactor';
import { applyHighlighting, computeDisplayText, raycastInteractive } from './shared';
import * as THREE from 'three';


export class XrInteractor implements Interactor {
    private explorer: Explorer;
    private selector: Selector;
    private controller: THREE.Group;
    private _intersectedObject: THREE.Object3D | null = null;
    private _selectedObject: THREE.Object3D | null = null;
    private grabbedObjectOriginalPosition: THREE.Vector3 | null = null;
    private textSprite: THREE.Sprite | null = null;
    // Runtime speed boost controlled by right-hand buttons (0: slow, 1: fast, else: normal)
    private boostMultiplier = 1;
    // Edge-detect state for XR button toggles
    private prevRightButton2 = false;

    constructor(explorer: Explorer, selector: Selector) {
        this.explorer = explorer;
        this.selector = selector;

        this.controller = this.explorer.renderer.xr.getController(1);

        type AnyController = THREE.Group & { addEventListener: (type: string, listener: (event: unknown) => void) => void };
        const anyController = this.controller as unknown as AnyController;
        anyController.addEventListener('selectstart', () => {
            const intersectedObject = this.raycastFromController();
            const cmd = this.selector.onSelectStart(intersectedObject);
            this._selectedObject = cmd.selectedObject;
            if (cmd.restoreOriginalPosition) {
                this.grabbedObjectOriginalPosition = this._selectedObject?.position.clone() ?? null;
            }
        });

        anyController.addEventListener('selectend', () => {
            const cmd = this.selector.onSelectEnd(this._selectedObject);
            if (cmd.restoreOriginalPosition && this._selectedObject) {
                this._selectedObject.position.copy(this.grabbedObjectOriginalPosition ?? new THREE.Vector3());
            }
            this._selectedObject = cmd.selectedObject;
            this.grabbedObjectOriginalPosition = null;
        });

        const laserGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(0, 0, 0),
            new THREE.Vector3(0, 0, -1)
        ]);

        const laserMaterial = new THREE.LineBasicMaterial({ color: this.explorer.config.laserColor });
        const laser = new THREE.Line(laserGeometry, laserMaterial);
        laser.scale.z = 5; // make it 5 units long

        this.controller.add(laser);

        this.explorer.cameraRig.add(this.controller);

        // XR enabling and VRButton creation handled by SwitchingInteractor.

    }

    // Expose current state to composition users via readonly accessors
    get intersectedObject(): THREE.Object3D | null { return this._intersectedObject; }
    get selectedObject(): THREE.Object3D | null { return this._selectedObject; }

    dispose(): void {
        if (this.textSprite) {
            this.controller.remove(this.textSprite);
            this.textSprite.material.map?.dispose();
            this.textSprite.material.dispose();
            this.textSprite = null;
        }
    }

    updateMovement(delta: number): void {
        const session = this.explorer.renderer.xr.getSession();
        if (!session) return;

        // Effective movement speed (units/second): base from config, scaled by runtime boost
        const movementSpeed = this.explorer.config.speedMultiplier * this.boostMultiplier;
        const rotationSpeed = 2;

        for (const inputSource of session.inputSources) {
            const gp = inputSource.gamepad;
            if (!gp || gp.axes.length < 2) continue;

            const x = gp.axes[2] ?? gp.axes[0];
            const y = gp.axes[3] ?? gp.axes[1];

            if (inputSource.handedness === 'left') {
                if (Math.abs(x) > 0.1 || Math.abs(y) > 0.1) {
                    const movement = new THREE.Vector3(x, 0, y)
                        .normalize()
                        .multiplyScalar(movementSpeed * delta)
                        .applyQuaternion(this.explorer.cameraRig.quaternion);
                    this.explorer.cameraRig.position.add(movement);
                }
                if (gp.buttons[0]?.pressed) {
                    this.explorer.cameraRig.position.y -= movementSpeed * delta;
                }
                if (gp.buttons[1]?.pressed) {
                    this.explorer.cameraRig.position.y += movementSpeed * delta;
                }
            } else if (inputSource.handedness === 'right') {
                if (Math.abs(x) > 0.1) {
                    const yaw = -x * rotationSpeed * delta;
                    const rotation = new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(0, 1, 0), yaw);
                    this.explorer.cameraRig.quaternion.multiply(rotation);
                }
                // Adjust runtime boost without overwriting the configured base multiplier
                if (gp.buttons[0]?.pressed) {
                    this.boostMultiplier = 0.1;   // slow
                } else if (gp.buttons[1]?.pressed) {
                    this.boostMultiplier = 10;    // fast
                } else {
                    this.boostMultiplier = 1;     // normal
                }

                // XR toggle: right controller button[2] switches specialTypeRing.innerHemisphere
                const b2 = !!gp.buttons[2]?.pressed;
                if (b2 && !this.prevRightButton2) {
                    const cfg = this.explorer.config;
                    // Flip innerHemisphere; keep isSpecialEnabled as-is (defaults to true).
                    cfg.specialTypeRing = {
                        ...cfg.specialTypeRing,
                        innerHemisphere: !cfg.specialTypeRing.innerHemisphere,
                    };
                    // Redraw to apply new gating: roles vs external sphere drawing
                    // Force a rebuild to apply new gating; safe to call directly
                    try { (this.explorer as unknown as { drawScene: () => void }).drawScene(); } catch {}
                }
                this.prevRightButton2 = b2;
            }
        }
    }

    updateInteraction(): void {
        const intersectedObject = this.raycastFromController();
        this._intersectedObject = intersectedObject;
        applyHighlighting(this.explorer, this._intersectedObject);
        const objectToDisplay = this._selectedObject || this._intersectedObject;
        const text = computeDisplayText(this.explorer, objectToDisplay);
        this.showTextSprite(text);
    }

    raycastFromController(): THREE.Object3D | null {
        const raycaster = new THREE.Raycaster();
        const laserStart = new THREE.Vector3(0, 0, 0);
        const laserEnd = new THREE.Vector3(0, 0, -1);
        this.controller.localToWorld(laserStart);
        this.controller.localToWorld(laserEnd);
        const direction = new THREE.Vector3().subVectors(laserEnd, laserStart).normalize();
        raycaster.ray.origin.copy(laserStart);
        raycaster.ray.direction.copy(direction);
        const intersects = raycastInteractive(this.explorer, raycaster);
        return intersects.length > 0 ? intersects[0].object : null;
    }

    private showTextSprite(text: string) {
        // Remove old sprite
        if (this.textSprite) {
            this.controller.remove(this.textSprite);
            this.textSprite.material.map?.dispose();
            this.textSprite.material.dispose();
            this.textSprite = null;
        }
        if (!text || text.trim() === '') return;

        // Word-wrap the text into lines
        const fontSize = 24;
        const padding = 40;
        const lineHeight = 28;
        const maxWidth = 512 - padding;

        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d')!;
        tempCtx.font = `${fontSize}px sans-serif`;

        const words = text.split(' ');
        let line = '';
        const lines: string[] = [];
        for (let i = 0; i < words.length; i++) {
            const testLine = line + words[i] + ' ';
            const testWidth = tempCtx.measureText(testLine).width;
            if (testWidth > maxWidth && line !== '') {
                lines.push(line.trim());
                line = words[i] + ' ';
            } else {
                line = testLine;
            }
        }
        if (line !== '') lines.push(line.trim());

        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = lines.length * lineHeight + 60;

        const ctx = canvas.getContext('2d')!;
        ctx.fillStyle = 'rgba(0,0,0,0.6)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = 'white';
        ctx.font = `${fontSize}px sans-serif`;
        ctx.textBaseline = 'top';

        lines.forEach((line, i) => {
            ctx.fillText(line, 20, 30 + i * lineHeight);
        });

        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        this.textSprite = new THREE.Sprite(material);
        const aspect = canvas.width / canvas.height;
        this.textSprite.scale.set(1.5 * aspect, 1.5, 1);
        this.textSprite.position.set(0, 0, -5);
        this.controller.add(this.textSprite);
    }
}
