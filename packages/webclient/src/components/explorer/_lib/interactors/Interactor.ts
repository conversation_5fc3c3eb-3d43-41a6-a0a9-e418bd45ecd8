import * as THREE from 'three';
import type Explorer from '../../Explorer';
import type { Selector } from '../types';

// Contract used by Explorer for user interaction in both XR and non-XR modes
export interface Interactor {
    // Update hover/select UI and highlighting
    updateInteraction(): void;
    // Update locomotion/rotation (XR typically; no-op for non-XR), given time delta in seconds
    updateMovement(delta: number): void;
    // Produce a raycast based on the active input device, returning the intersected object or null
    raycastFromController(): THREE.Object3D | null;
    // The most recent object under the cursor/laser (null when none)
    readonly intersectedObject: THREE.Object3D | null;
    // The object currently selected/active (null when none)
    readonly selectedObject: THREE.Object3D | null;
    // Cleanup any DOM or three.js resources/listeners
    dispose(): void;
}

// Factory signature for creating an Interactor instance
export type InteractorFactory = (explorer: Explorer, selector: Selector) => Interactor;
