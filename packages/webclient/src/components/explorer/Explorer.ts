import type { <PERSON>rim<PERSON><PERSON><PERSON><PERSON><PERSON>, FormatMeta<PERSON>son, TypeMetaJson, SignatureMetaJson, ImplementationMetaJson } from '@toolproof/_schemas';
import type { PrimitiveConst } from '@toolproof/_lib/types';
import type { ResourceMerged } from '@/types';
import type { ExplorerConfig, PrimitiveMeshMap, ResourceMeshMap, MeshConfig, NormalDirection, SphereConfig, BoxConfig, OrientationMode, RoleInSignature } from './_lib/types';
import type { Interactor } from './_lib/interactors/Interactor';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { addRealisticDummies, computeBasisFromNormal, augmentResourcesWithDummies, augmentImplementationsWithDummies } from './_lib/utils';
import { meshConfigFormats, meshConfigTypes, meshConfigRoles, meshConfigSignatures, meshConfigImplementations, primitiveCounts, orientationMode } from './_lib/config';
import { createCamera } from './_lib/components/camera';
import { createLights } from './_lib/components/lights';
import { createScene } from './_lib/components/scene';
import { createRenderer } from './_lib/systems/renderer';
import { createControls } from './_lib/systems/controls';
import { Resizer } from './_lib/systems/Resizer';
import * as THREE from 'three';


export default class Explorer {
    config: ExplorerConfig;
    renderer;
    scene;
    camera;
    cameraRig = new THREE.Group();
    private clock = new THREE.Clock();
    private interactor: Interactor;
    private currentRolesSignatureId: string | null = null;

    private formatsMeta: FormatMetaJson[] = [];
    private typesMeta: TypeMetaJson[] = [];
    private signaturesMeta: SignatureMetaJson[] = [];
    private implementationsMeta: ImplementationMetaJson[] = [];
    private resourcesMerged: Record<string, ResourceMerged[]> = {} as Record<string, ResourceMerged[]>;

    primitiveMeshMap: PrimitiveMeshMap = {
        formats: [],
        types: [],
        roles: [],
        signatures: []
    } as PrimitiveMeshMap;
    private resourceMeshMap: ResourceMeshMap = {} as ResourceMeshMap;

    constructor(container: HTMLDivElement, config: ExplorerConfig) {
        // Clone the incoming config to avoid mutating React props (which may be frozen in dev)
        this.config = { ...config };
        this.renderer = createRenderer();
        this.scene = createScene(config.skyColor);
        const { ambientLight, mainLight } = createLights();
        this.camera = createCamera(30);
        this.cameraRig.add(this.camera);
        createControls(this.camera, this.renderer.domElement);

        // Ensure we don't stack multiple canvases if Explorer is re-created (e.g., React StrictMode)
        try {
            while (container.firstChild) container.removeChild(container.firstChild);
        } catch { }
        container.append(this.renderer.domElement);
        new Resizer(container, this.camera, this.renderer);

        this.scene.add(ambientLight, mainLight, this.cameraRig);

        this.interactor = this.config.interactorFactory(this, this.config.selector);

    }

    async init() {
        // Build scene, but don't let failures prevent the render loop from starting
        try {
            this.drawScene();

            // ATTENTION: should we move the cameraRig instead?
            // Position camera to get a good view of all circles
            this.camera.position.set(0, 5, 50);
            this.camera.lookAt(0, 0, 0);
        } catch (err) {
            // Log but proceed to start the loop so background and future updates work
            console.error('[Explorer.init] createScene failed:', err);
        } finally {
            this.start();
        }
    }

    private start() {
        this.renderer.setAnimationLoop(() => {
            this.interactor.updateMovement(this.clock.getDelta());
            this.interactor.updateInteraction();

            // Update role ring and links based on selected signature
            try {
                const allowRoles = !!(this.config.specialTypeRing?.isSpecialEnabled) && !!(this.config.specialTypeRing?.innerHemisphere);
                if (!allowRoles) {
                    // If roles are not allowed by config, ensure any previous roles UI is cleared
                    if (this.currentRolesSignatureId !== null) {
                        this.clearRolesUI();
                        this.currentRolesSignatureId = null;
                    }
                } else if (this.config.dummyConfig.showAllSignatureRoles) {
                    // Draw once for all signatures and avoid redrawing every frame
                    if (this.currentRolesSignatureId !== '__ALL__') {
                        this.currentRolesSignatureId = '__ALL__';
                        this.clearRolesUI();
                        for (const sig of this.signaturesMeta) {
                            this.drawRolesForSignature(sig as SignatureMetaJson);
                        }
                    }
                } else {
                    const sel = this.interactor.selectedObject;
                    const isSignature = !!sel && sel.userData?.entity === CONSTANTS.PRIMITIVES.signatures;
                    const sigId: string | null = isSignature ? (sel.userData?.id ?? null) : null;
                    if (sigId !== this.currentRolesSignatureId) {
                        this.currentRolesSignatureId = sigId;
                        this.clearRolesUI();
                        if (sigId) {
                            const sigMeta = this.signaturesMeta.find(s => s.id === sigId);
                            if (sigMeta) this.drawRolesForSignature(sigMeta as SignatureMetaJson);
                        }
                    }
                }
            } catch { /* ignore transient selection issues */ }

            this.renderer.render(this.scene, this.camera);
        });
    }

    // Attach a small flag above a mesh to indicate dummy status
    private attachFlag(parent: THREE.Mesh, options?: { height?: number; color?: THREE.ColorRepresentation }) {
        const height = options?.height ?? 0.5;
        const color = options?.color ?? 0xffaa00;

        // Compute vertical half-extent from geometry; prefer boundingBox height over boundingSphere radius
        let base = 0.9;
        const geom = parent.geometry as THREE.BufferGeometry | undefined;
        if (geom) {
            // Try bounding box first (accurate for boxes and works for spheres too)
            geom.computeBoundingBox();
            const box = geom.boundingBox;
            const scaleY = parent.scale?.y ?? 1;
            if (box) {
                const heightY = (box.max.y - box.min.y) * scaleY;
                base = heightY / 2;
            } else {
                // Fallback: bounding sphere radius
                geom.computeBoundingSphere();
                const r = geom.boundingSphere?.radius;
                if (typeof r === 'number' && isFinite(r)) {
                    base = r * scaleY;
                }
            }
        }

        const group = new THREE.Group();
        group.name = 'dummyFlag';

        // Thin pole
        const poleRadius = 0.02;
        const poleGeom = new THREE.CylinderGeometry(poleRadius, poleRadius, height, 8);
        const poleMat = new THREE.MeshBasicMaterial({ color: 0x333333 });
        const pole = new THREE.Mesh(poleGeom, poleMat);
        pole.position.set(0, base + height / 2, 0);
        group.add(pole);

        // Simple flag (small plane) sized proportionally to the pole height
        const flagWidth = height * 0.5;
        const flagHeight = height * 0.3;
        const flagGeom = new THREE.PlaneGeometry(flagWidth, flagHeight);
        const flagMat = new THREE.MeshBasicMaterial({ color, side: THREE.DoubleSide });
        const flag = new THREE.Mesh(flagGeom, flagMat);
        flag.position.set(0, base + height - flagHeight / 2, poleRadius + flagWidth / 2);
        flag.rotateY(Math.PI / 2);
        group.add(flag);

        parent.add(group);
    }

    private drawScene() {

        // ATTENTION: Hook for tests
        if (true) {
            const geo = new THREE.BoxGeometry(5, 5, 5);
            const mat = new THREE.MeshStandardMaterial({ color: 0xff0000 }); // red
            const mesh = new THREE.Mesh(geo, mat);
            this.scene.add(mesh);

            const angleRad = THREE.MathUtils.degToRad(45);
            const cos = Math.cos(angleRad);
            const sin = Math.sin(angleRad);
            const sx = 2;   // scale X
            const sy = 0.5; // scale Y
            const sz = 1;   // scale Z

            const matrix = new THREE.Matrix4().set(
                cos * sx, -sin * sy, 0, -25,
                sin * sx, cos * sy, 0, 2,
                0, 0, sz, -6,
                0, 0, 0, 1
            );
            mesh.applyMatrix4(matrix);

            return;
        }


        // Clear existing objects from previous runs
        // Re-initialize with proper arrays to avoid undefined on push
        this.primitiveMeshMap = {
            formats: [],
            types: [],
            roles: [],
            signatures: [],
        } as PrimitiveMeshMap;
        const toRemove: THREE.Object3D[] = [];
        this.scene.traverse((child) => {
            if (
                // Remove by group/object names for higher-level containers
                child.name === CONSTANTS.PRIMITIVES.formats ||
                child.name === CONSTANTS.PRIMITIVES.types ||
                child.name === CONSTANTS.PRIMITIVES.roles ||
                child.name === CONSTANTS.PRIMITIVES.signatures ||
                child.name === CONSTANTS.PRIMITIVES.implementations ||
                child.name === CONSTANTS.STORAGE.resources ||
                // Remove prior ring guide lines/tubes (named with '-ring-guide')
                (typeof child.name === 'string' && (child.name.endsWith('-ring-guide') || child.name.endsWith('-sphere-guide')))
            ) {
                toRemove.push(child);
            }
        });
        toRemove.forEach((obj) => obj.parent?.remove(obj));

        // Optionally create dummy compositions for visibility during development
        if (this.config.dummyConfig.dummiesEnabled) {
            const composed = addRealisticDummies({
                formats: this.formatsMeta,
                types: this.typesMeta,
                signatures: this.signaturesMeta,
                counts: primitiveCounts
            });
            this.formatsMeta = composed.formats;
            this.typesMeta = composed.types;
            this.signaturesMeta = composed.signatures;
            // Also synthesize dummy resources for dummy types and for types referenced by dummy signatures
            this.resourcesMerged = augmentResourcesWithDummies({
                resourcesByType: this.resourcesMerged,
                types: this.typesMeta,
                signatures: this.signaturesMeta,
                opts: {
                    perDummyType: this.config.dummyConfig.externalBounds?.resourcesPerDummyType,
                    perSignatureRoleType: this.config.dummyConfig.externalBounds?.resourcesPerDummySignatureRoleType,
                }
            });
            // And synthesize dummy implementations for dummy signatures
            this.implementationsMeta = augmentImplementationsWithDummies({
                implementations: this.implementationsMeta,
                signatures: this.signaturesMeta,
                opts: { perDummySignature: this.config.dummyConfig.externalBounds?.implementationsPerDummySignature }
            });
        }

        // Draw scene in modular steps
        // this.drawDummyCube();
        this.drawFormats();
        this.drawTypes();
        this.drawSignatures();
        this.drawImplementations();
        this.drawResources();
    }

    // Draw a guide ring using an explicit basis (u, v) in the plane
    private drawRingGuideBasis(
        primitiveConst: string,
        center: THREE.Vector3,
        radius: number,
        count: number,
        basis: { u: THREE.Vector3; v: THREE.Vector3 },
        options?: {
            guideColor?: THREE.ColorRepresentation;
            guideSegments?: number;
            guideThickness?: number;
            guideVisible?: boolean;
            orientationMode?: OrientationMode;
            origin?: THREE.Vector3; // reference point for radial/tangent; default (0,0,0)
            up?: THREE.Vector3; // up vector for tangent; default (0,1,0)
            // Optional explicit ring radius override (useful when geometry kind isn't 'sphere')
            ringRadius?: number;
        }
    ) {
        const guideVisible = options?.guideVisible ?? true;
        if (!guideVisible) return;

        const segments = options?.guideSegments ?? Math.max(64, count * 12);
        const color = options?.guideColor ?? 0x888888;
        const thickness = options?.guideThickness ?? 0;
        // Determine effective basis according to orientation toggle
        let { u, v } = basis;
        const mode = options?.orientationMode ?? 'given';
        if (mode !== 'given') {
            const origin = options?.origin ?? new THREE.Vector3(0, 0, 0);
            const up = (options?.up ?? new THREE.Vector3(0, 1, 0)).clone().normalize();
            // Radial vector from origin to center, projected onto plane perpendicular to up
            const radial = center.clone().sub(origin);
            // Project out the up component: radial -= up * (radial·up)
            const upComp = up.clone().multiplyScalar(radial.dot(up));
            radial.sub(upComp);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0); // fallback
            radial.normalize();
            let normal: THREE.Vector3;
            if (mode === 'radial') {
                normal = radial;
            } else {
                // tangent-based normal = up x radial
                normal = new THREE.Vector3().crossVectors(up, radial);
                if (normal.lengthSq() < 1e-6) normal.set(0, 0, -1);
                normal.normalize();
            }
            const b = computeBasisFromNormal(normal);
            u = b.u; v = b.v;
        }

        const circlePoints: THREE.Vector3[] = [];
        for (let s = 0; s <= segments; s++) {
            const t = (s / segments) * Math.PI * 2;
            const pt = center.clone()
                .add(u.clone().multiplyScalar(Math.cos(t) * radius))
                .add(v.clone().multiplyScalar(Math.sin(t) * radius));
            circlePoints.push(pt);
        }

        if (thickness > 0) {
            const curve = new THREE.CatmullRomCurve3(circlePoints, true, 'catmullrom', 0.0);
            const tubularSegments = Math.max(segments * 2, 128);
            const radialSegments = 8;
            const tubeGeom = new THREE.TubeGeometry(curve, tubularSegments, thickness, radialSegments, true);
            // Non-occluding, faint guide; draw late to be on top visually but without depth side-effects
            const tubeMat = new THREE.MeshBasicMaterial({ color, depthTest: false, depthWrite: false, transparent: true, opacity: 0.35 });
            const tube = new THREE.Mesh(tubeGeom, tubeMat);
            tube.name = `${primitiveConst}-ring-guide`;
            tube.renderOrder = 9999;
            this.scene.add(tube);
        } else {
            const circleGeom = new THREE.BufferGeometry().setFromPoints(circlePoints);
            const circleMat = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.5, depthTest: false, depthWrite: false });
            const circleLine = new THREE.LineLoop(circleGeom, circleMat);
            circleLine.name = `${primitiveConst}-ring-guide`;
            circleLine.renderOrder = 9999;
            this.scene.add(circleLine);
        }
    }

    // Generic ring drawer using an explicit basis (u, v) for the ring plane
    private drawRingBasis(
        entityConst: PrimitiveConst,
        entities: PrimitiveMetaJson[],
        center: THREE.Vector3,
        meshConfig: MeshConfig<SphereConfig | BoxConfig>,
        basis: { u: THREE.Vector3; v: THREE.Vector3 },
        options?: {
            guideColor?: THREE.ColorRepresentation;
            guideSegments?: number;
            guideThickness?: number;
            guideVisible?: boolean;
            orientationMode?: OrientationMode;
            origin?: THREE.Vector3; // reference point for radial/tangent; default (0,0,0)
            up?: THREE.Vector3; // up vector for tangent; default (0,1,0)
            // If provided, entities will be split across two semicircles relative to world +Y half-space.
            // The discriminator should return 'upper'/'input' for top half, 'lower'/'output' for bottom half.
            semicircleBy?: (entity: PrimitiveMetaJson) => 'upper' | 'lower' | 'input' | 'output';
            // If provided, supply a per-entity angle (radians) on the ring; overrides default equal spacing when no semicircle split.
            angleBy?: (entity: PrimitiveMetaJson, index: number, count: number) => number;
            // Optional: override dummy detection per entity
            dummyBy?: (entity: PrimitiveMetaJson) => boolean;
            // Optional: override the display name per entity (for tooltips/UI only)
            nameBy?: (entity: PrimitiveMetaJson) => string;
            // Optional explicit ring radius override (useful when geometry kind isn't 'sphere')
            ringRadius?: number;
        }
    ) {
        const count = entities.length;
        // Determine effective basis according to orientation toggle
        let { u, v } = basis;
        const mode = options?.orientationMode ?? 'given';
        if (mode !== 'given') {
            const origin = options?.origin ?? new THREE.Vector3(0, 0, 0);
            const up = (options?.up ?? new THREE.Vector3(0, 1, 0)).clone().normalize();
            // Radial vector from origin to center, projected onto plane perpendicular to up
            const radial = center.clone().sub(origin);
            const upComp = up.clone().multiplyScalar(radial.dot(up));
            radial.sub(upComp);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
            radial.normalize();
            let normal: THREE.Vector3;
            if (mode === 'radial') {
                normal = radial;
            } else {
                normal = new THREE.Vector3().crossVectors(up, radial);
                if (normal.lengthSq() < 1e-6) normal.set(0, 0, -1);
                normal.normalize();
            }
            const b = computeBasisFromNormal(normal);
            u = b.u; v = b.v;
        }

        // Determine ring radius from geometry (sphere configs carry ringRadius) or option override
        const ringRadius: number = (options?.ringRadius !== undefined)
            ? options.ringRadius
            : ('ringRadius' in meshConfig.geometry
                ? (meshConfig.geometry as SphereConfig).ringRadius
                : 0); // default 0 for non-ring geometries unless overridden

        // Draw the guide ring with the provided basis
        this.drawRingGuideBasis(
            entityConst,
            center,
            ringRadius,
            count,
            { u, v },
            options
        );

        // Helper to create a mesh for an entity at a given angle on the ring
        const placeEntityAtAngle = (primitive: PrimitiveMetaJson, angle: number) => {
            const offset = u.clone().multiplyScalar(Math.cos(angle) * ringRadius)
                .add(v.clone().multiplyScalar(Math.sin(angle) * ringRadius));

            let geometry: THREE.BufferGeometry;
            switch (meshConfig.geometry.kind) {
                case 'sphere':
                    geometry = new THREE.SphereGeometry(
                        meshConfig.geometry.radius,
                        meshConfig.geometry.widthSegments,
                        meshConfig.geometry.heightSegments
                    );
                    break;
                case 'box':
                    geometry = new THREE.BoxGeometry(
                        meshConfig.geometry.width,
                        meshConfig.geometry.height,
                        meshConfig.geometry.depth,
                        meshConfig.geometry.widthSegments,
                        meshConfig.geometry.heightSegments,
                        meshConfig.geometry.depthSegments
                    );
                    break;
                default:
                    throw new Error('Unsupported geometry kind');
            }

            const material = new THREE.MeshStandardMaterial({
                color: meshConfig.material.color,
                metalness: meshConfig.material.metalness,
                roughness: meshConfig.material.roughness,
                emissive: meshConfig.material.emissive,
            });
            const mesh = new THREE.Mesh(geometry, material.clone());
            mesh.position.copy(center).add(offset);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            // Determine dummy status via override or name pattern
            const isDummy = options?.dummyBy ? options.dummyBy(primitive) : String(primitive.name).toLowerCase().includes('dummy');
            const displayName = options?.nameBy ? options.nameBy(primitive) : (primitive.name ?? primitive.id);
            this.attachFlag(mesh, { height: this.config.dummyConfig.dummyIndicatorPoleHeight, color: isDummy ? this.config.dummyConfig.dummyFlagColor : this.config.dummyConfig.nonDummyFlagColor });
            mesh.userData = { entity: entityConst, id: primitive.id, name: displayName, isDummy };

            const group = new THREE.Group();
            group.name = entityConst;
            group.add(mesh);
            this.scene.add(group);

            switch (entityConst) {
                case CONSTANTS.PRIMITIVES.formats:
                    this.primitiveMeshMap.formats.push({ ...(primitive as FormatMetaJson), mesh });
                    break;
                case CONSTANTS.PRIMITIVES.types:
                    this.primitiveMeshMap.types.push({ ...(primitive as TypeMetaJson), mesh });
                    break;
                case CONSTANTS.PRIMITIVES.roles:
                    this.primitiveMeshMap.roles.push({ ...(primitive as RoleInSignature), name: String(displayName), mesh });
                    break;
                case CONSTANTS.PRIMITIVES.signatures:
                    this.primitiveMeshMap.signatures.push({ ...(primitive as SignatureMetaJson), mesh });
                    break;
                default:
                    break;
            }
        };

        const discriminator = options?.semicircleBy;
        if (!discriminator) {
            const angleProvider = options?.angleBy;
            if (angleProvider) {
                for (let i = 0; i < count; i++) {
                    const angle = angleProvider(entities[i], i, count);
                    placeEntityAtAngle(entities[i], angle);
                }
            } else {
                // Default: distribute on full circle
                for (let i = 0; i < count; i++) {
                    const angle = (i / count) * Math.PI * 2;
                    placeEntityAtAngle(entities[i], angle);
                }
            }
        } else {
            // Split entities across upper (y>0) and lower (y<0) half-spaces
            const uppers: PrimitiveMetaJson[] = [];
            const lowers: PrimitiveMetaJson[] = [];
            for (const e of entities) {
                const tag = discriminator(e);
                if (tag === 'upper' || tag === 'input') uppers.push(e); else lowers.push(e);
            }

            // Determine phase so that y>0 corresponds to angles in (alpha-π/2, alpha+π/2)
            const uy = u.y; const vy = v.y;
            const mag = Math.hypot(uy, vy);
            let alpha = 0;
            if (mag > 1e-6) {
                alpha = Math.atan2(vy, uy);
            } else {
                // Degenerate: fall back to using v as 'up' reference in-plane
                alpha = 0; // split at 0 and π; will not introduce y separation if plane is horizontal
            }

            const placeHalf = (arr: PrimitiveMetaJson[], start: number) => {
                const n = arr.length;
                if (n === 0) return;
                for (let i = 0; i < n; i++) {
                    // Center items within the half-arc
                    const angle = start + ((i + 0.5) / n) * Math.PI;
                    placeEntityAtAngle(arr[i], angle);
                }
            };

            // Upper half: centered around alpha (from alpha-π/2 to alpha+π/2)
            placeHalf(uppers, alpha - Math.PI / 2);
            // Lower half: opposite semicircle
            placeHalf(lowers, alpha + Math.PI / 2);
        }
    }

    private drawRing(
        entityConst: PrimitiveConst,
        entities: PrimitiveMetaJson[],
        normal: NormalDirection,
        center: THREE.Vector3,
        meshConfig: MeshConfig<SphereConfig | BoxConfig>,
        options?: {
            guideColor?: THREE.ColorRepresentation;
            guideSegments?: number; // segments along the circle
            guideThickness?: number; // if > 0, draws a tube with this radius instead of a 1px line
            guideVisible?: boolean;
        }
    ) {
        // Choose an orthonormal basis (u, v) for the plane perpendicular to the given normal.
        // - 'y' => ring lies in xz-plane
        // - 'x' => ring lies in yz-plane
        // - 'z' => ring lies in xy-plane
        let u: THREE.Vector3; // first basis vector in the ring's plane
        let v: THREE.Vector3; // second basis vector in the ring's plane
        switch (normal) {
            case 'y':
                u = new THREE.Vector3(1, 0, 0);
                v = new THREE.Vector3(0, 0, 1);
                break;
            case 'x':
                u = new THREE.Vector3(0, 1, 0);
                v = new THREE.Vector3(0, 0, 1);
                break;
            case 'z':
                u = new THREE.Vector3(1, 0, 0);
                v = new THREE.Vector3(0, 1, 0);
                break;
        }
        // Delegate to basis-driven ring drawer
        this.drawRingBasis(entityConst, entities, center, meshConfig, { u, v }, options);
    }

    private drawFormats() {
        const center = new THREE.Vector3(0, 0, 0);
        const u = new THREE.Vector3(1, 0, 0);
        const v = new THREE.Vector3(0, 0, 1);
        const entities = this.formatsMeta as unknown as PrimitiveMetaJson[];
        // IDs for special formats that must be opposite each other
        const A = CONSTANTS.SPECIALS.FORMAT_ApplicationJson;
        const B = CONSTANTS.SPECIALS.FORMAT_ApplicationJob;

        // Prepare an angle mapping if both are present and not dummy
        const hasA = entities.some((e) => e.id === A && !String(e.name).toLowerCase().includes('dummy'));
        const hasB = entities.some((e) => e.id === B && !String(e.name).toLowerCase().includes('dummy'));
        // console.log('hasA:', hasA, 'hasB:', hasB);

        if (hasA && hasB) {
            // Choose a stable base angle; 0 aligns A along +X, B along -X
            const angleMap = new Map<string, number>();
            angleMap.set(A, 0);
            angleMap.set(B, Math.PI);

            // Determine other entities in deterministic order
            const others = entities.filter((e) => e.id !== A && e.id !== B);
            const nOthers = others.length;
            // Evenly distribute others between the two arcs: (0, π) and (π, 2π)
            // We interleave to keep rough balance visually
            let idxUpper = 0; // between 0 and π
            let idxLower = 0; // between π and 2π
            const upperCount = Math.ceil(nOthers / 2);
            const lowerCount = nOthers - upperCount;
            for (let i = 0; i < nOthers; i++) {
                const e = others[i];
                if (i % 2 === 0) {
                    const t = (idxUpper + 1) / (upperCount + 1); // avoid colliding with endpoints
                    angleMap.set(e.id, t * Math.PI);
                    idxUpper++;
                } else {
                    const t = (idxLower + 1) / (lowerCount + 1);
                    angleMap.set(e.id, Math.PI + t * Math.PI);
                    idxLower++;
                }
            }
            this.drawRingBasis(
                CONSTANTS.PRIMITIVES.formats,
                entities,
                center,
                meshConfigFormats,
                { u, v },
                {
                    angleBy: (entity) => angleMap.get(entity.id) ?? 0,
                    orientationMode: 'given',
                }
            );
        } else {
            // Fallback to default distribution
            this.drawRing(CONSTANTS.PRIMITIVES.formats, this.formatsMeta, 'y', center, meshConfigFormats, { guideVisible: true });
        }
    }

    private drawTypes() {

        const formatTest_0 = CONSTANTS.SPECIALS.FORMAT_Test_0;

        // For each format mesh, draw a ring of its composing types around it.
        // Orientation is tangent-based: the child ring's plane normal points along
        // the tangent direction of the parent formats ring at that format's position.
        // This makes the plane spanned by (radial, up) which improves legibility and routing.
        const formats = this.primitiveMeshMap.formats;
        if (!formats || formats.length === 0) return;

        // Debug: quick glance at linkage universe
        try {
            const presentFormatIds = new Set(this.primitiveMeshMap.formats.map(f => f.id));
            const sampleTypeFormatIds = Array.from(new Set(this.typesMeta.slice(0, 10).map(t => t.formatId)));
            // console.log('[Explorer] drawTypes: formats present:', presentFormatIds.size, 'sample type.formatIds:', sampleTypeFormatIds);
        } catch { }

        formats.forEach((fmt) => {
            const center = fmt.mesh.position.clone();
            const typesForFormat = this.typesMeta.filter((t: TypeMetaJson) => t.formatId === fmt.id);
            if (typesForFormat.length === 0) return;
            // Special layout for FORMAT_Test_0: place all type meshes on either the inner or outer
            // hemisphere of the type ring relative to the world origin (format ring), controlled by config.
            if (fmt.id === formatTest_0) {
                // Compute inward direction from this format center toward the world origin on xz-plane
                const origin = new THREE.Vector3(0, 0, 0);
                const toOrigin = origin.clone().sub(center);
                const toOriginXZ = new THREE.Vector3(toOrigin.x, 0, toOrigin.z);
                let innerCenterAngle = 0;
                if (toOriginXZ.lengthSq() > 1e-8) {
                    toOriginXZ.normalize();
                    innerCenterAngle = Math.atan2(toOriginXZ.z, toOriginXZ.x);
                }

                // Determine hemisphere: use global specialTypeRing.innerHemisphere when present,
                // otherwise default to false.
                const useInner = !!(this.config.specialTypeRing?.innerHemisphere);
                const centerAngle = useInner ? innerCenterAngle : innerCenterAngle + Math.PI;

                const entities = typesForFormat as unknown as PrimitiveMetaJson[];
                // Ensure the special type (WorkflowSpec) is always placed at the exact center
                // of the chosen half-arc (directly inward for inner hemisphere, or directly
                // outward for outer hemisphere). Distribute the remaining types evenly on the
                // left/right quarters around it.
                const specialTypeId = CONSTANTS.SPECIALS.TYPE_WorkflowSpec;
                const angleMap = new Map<string, number>();
                const hasSpecial = entities.some(e => e.id === specialTypeId);
                if (hasSpecial) {
                    angleMap.set(specialTypeId, centerAngle);
                    const others = entities.filter(e => e.id !== specialTypeId);
                    const m = others.length;
                    const leftCount = Math.floor(m / 2);
                    const rightCount = m - leftCount;
                    if (leftCount > 0) {
                        const leftStep = (Math.PI / 2) / leftCount; // spread over (-π/2, 0)
                        for (let j = 0; j < leftCount; j++) {
                            const offset = -Math.PI / 2 + (j + 0.5) * leftStep;
                            angleMap.set(others[j].id, centerAngle + offset);
                        }
                    }
                    if (rightCount > 0) {
                        const rightStep = (Math.PI / 2) / rightCount; // spread over (0, +π/2)
                        for (let j = 0; j < rightCount; j++) {
                            const offset = (j + 0.5) * rightStep;
                            angleMap.set(others[leftCount + j].id, centerAngle + offset);
                        }
                    }
                }
                this.drawRingBasis(
                    CONSTANTS.PRIMITIVES.types,
                    entities,
                    center,
                    meshConfigTypes,
                    { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) },
                    {
                        orientationMode: 'given',
                        // Distribute all items evenly across a half-arc centered at centerAngle.
                        // This keeps them strictly on the chosen hemisphere relative to the format ring.
                        angleBy: (entity, i, count) => {
                            const mapped = angleMap.get(entity.id);
                            if (mapped !== undefined) return mapped;
                            // Fallback: even spacing across the half-arc
                            return centerAngle - Math.PI / 2 + ((i + 0.5) / count) * Math.PI;
                        },
                    }
                );
                return;
            }

            // Debug: log linkage for this format
            try {
                // console.log(`[Explorer] types for format ${fmt.name} (${fmt.id}):`, typesForFormat.length, 'example ids:', typesForFormat.slice(0, 3).map(t => t.id));
            } catch { }
            // Special alignment for format A: ensure specific type points inward toward the special signature position
            const formatAId = CONSTANTS.SPECIALS.FORMAT_ApplicationJson;
            const specialTypeId = CONSTANTS.SPECIALS.TYPE_WorkflowSpec;

            if (fmt.id === formatAId && !this.config.specialTypeRing?.isSpecialEnabled) {
                // Predict special signature position on signatures ring around format B
                const formatBId = CONSTANTS.SPECIALS.FORMAT_ApplicationJob;
                const formatBPos = this.primitiveMeshMap.formats.find((f) => f.id === formatBId)?.mesh.position.clone();
                const specialTypeIndex = typesForFormat.findIndex((t) => t.id === specialTypeId);

                if (formatBPos && specialTypeIndex >= 0) {
                    const ringRadiusSignatures: number = 'ringRadius' in meshConfigTypes.geometry
                        ? (meshConfigTypes.geometry as SphereConfig).ringRadius
                        : 26;
                    // Direction from B to A, projected to xz
                    const dirBtoA = center.clone().sub(formatBPos);
                    const dirBtoA_XZ = new THREE.Vector3(dirBtoA.x, 0, dirBtoA.z);
                    if (dirBtoA_XZ.lengthSq() > 1e-8) dirBtoA_XZ.normalize(); else dirBtoA_XZ.set(1, 0, 0);
                    const predictedSignaturePos = formatBPos.clone().add(dirBtoA_XZ.clone().multiplyScalar(ringRadiusSignatures));

                    // Inward direction from format A center to predicted signature position
                    const dirAtoSignature = predictedSignaturePos.clone().sub(center);
                    const dirAtoSignature_XZ = new THREE.Vector3(dirAtoSignature.x, 0, dirAtoSignature.z);
                    let inwardAngle = 0;
                    if (dirAtoSignature_XZ.lengthSq() > 1e-8) {
                        dirAtoSignature_XZ.normalize();
                        inwardAngle = Math.atan2(dirAtoSignature_XZ.z, dirAtoSignature_XZ.x);
                    }

                    const entities = typesForFormat as unknown as PrimitiveMetaJson[];
                    const n = entities.length;
                    const baseAngleSpecial = (specialTypeIndex / n) * Math.PI * 2;
                    const delta = inwardAngle - baseAngleSpecial;

                    this.drawRingBasis(
                        CONSTANTS.PRIMITIVES.types,
                        entities,
                        center,
                        meshConfigTypes,
                        { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) },
                        {
                            orientationMode: 'given',
                            angleBy: (_entity, i, count) => ((i / count) * Math.PI * 2) + delta,
                        }
                    );
                    return;
                }
            }

            // If global special toggles are enabled, apply the same half-arc placement to every format
            if (this.config.specialTypeRing?.isSpecialEnabled) {
                // Compute inward direction from this format center toward the world origin on xz-plane
                const origin = new THREE.Vector3(0, 0, 0);
                const toOrigin = origin.clone().sub(center);
                const toOriginXZ = new THREE.Vector3(toOrigin.x, 0, toOrigin.z);
                let innerCenterAngle = 0;
                if (toOriginXZ.lengthSq() > 1e-8) {
                    toOriginXZ.normalize();
                    innerCenterAngle = Math.atan2(toOriginXZ.z, toOriginXZ.x);
                }

                const useInner = !!(this.config.specialTypeRing?.innerHemisphere);
                const centerAngle = useInner ? innerCenterAngle : innerCenterAngle + Math.PI;

                const entities = typesForFormat as unknown as PrimitiveMetaJson[];
                // Ensure the special type (WorkflowSpec) is always at the half-arc center
                const specialTypeId = CONSTANTS.SPECIALS.TYPE_WorkflowSpec;
                const angleMap = new Map<string, number>();
                const hasSpecial = entities.some(e => e.id === specialTypeId);
                if (hasSpecial) {
                    angleMap.set(specialTypeId, centerAngle);
                    const others = entities.filter(e => e.id !== specialTypeId);
                    const m = others.length;
                    const leftCount = Math.floor(m / 2);
                    const rightCount = m - leftCount;
                    if (leftCount > 0) {
                        const leftStep = (Math.PI / 2) / leftCount; // (-π/2, 0)
                        for (let j = 0; j < leftCount; j++) {
                            const offset = -Math.PI / 2 + (j + 0.5) * leftStep;
                            angleMap.set(others[j].id, centerAngle + offset);
                        }
                    }
                    if (rightCount > 0) {
                        const rightStep = (Math.PI / 2) / rightCount; // (0, +π/2)
                        for (let j = 0; j < rightCount; j++) {
                            const offset = (j + 0.5) * rightStep;
                            angleMap.set(others[leftCount + j].id, centerAngle + offset);
                        }
                    }
                }
                this.drawRingBasis(
                    CONSTANTS.PRIMITIVES.types,
                    entities,
                    center,
                    meshConfigTypes,
                    { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) },
                    {
                        orientationMode: 'given',
                        angleBy: (entity, i, count) => {
                            const mapped = angleMap.get(entity.id);
                            if (mapped !== undefined) return mapped;
                            // Fallback: even spacing across the half-arc
                            return centerAngle - Math.PI / 2 + ((i + 0.5) / count) * Math.PI;
                        },
                    }
                );
                return;
            }

            // Default behavior: fixed xz-plane basis around the format center; normal is +Y
            this.drawRingBasis(
                CONSTANTS.PRIMITIVES.types,
                typesForFormat as unknown as PrimitiveMetaJson[],
                center,
                meshConfigTypes,
                { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) },
                { orientationMode: 'given' }
            );
        });
    }

    // Remove any previously drawn role meshes, their ring guide, and link lines
    private clearRolesUI() {
        const toRemove: THREE.Object3D[] = [];
        const rolesName = CONSTANTS.PRIMITIVES.roles;
        const rolesGuideName = `${rolesName}-ring-guide`;
        const linksName = 'roles-links';
        this.scene.traverse((child) => {
            if (child.name === rolesName || child.name === rolesGuideName || child.name === linksName) {
                toRemove.push(child);
            }
        });
        toRemove.forEach((obj) => obj.parent?.remove(obj));
        // Reset runtime role meshes
        this.primitiveMeshMap.roles = [] as unknown as typeof this.primitiveMeshMap.roles;
    }

    // Draw a roles ring around the type(s) referenced by a selected signature and link lines from the signature
    private drawRolesForSignature(signature: SignatureMetaJson) {
        const roles = (signature as unknown as { roles?: { inputs?: Record<string, string>; outputs?: Record<string, string> } }).roles;
        if (!roles) return;
        const inputsEntries = Object.entries(roles.inputs ?? {}); // [roleId, typeId]
        const outputsEntries = Object.entries(roles.outputs ?? {});
        if (inputsEntries.length === 0 && outputsEntries.length === 0) return;

        // Group role ids by target type id
        const byType = new Map<string, { inputs: string[]; outputs: string[] }>();
        for (const [roleId, typeId] of inputsEntries) {
            const g = byType.get(typeId) ?? { inputs: [], outputs: [] };
            g.inputs.push(roleId); byType.set(typeId, g);
        }
        for (const [roleId, typeId] of outputsEntries) {
            const g = byType.get(typeId) ?? { inputs: [], outputs: [] };
            g.outputs.push(roleId); byType.set(typeId, g);
        }

        // Find the signature mesh to connect lines from
        const sigMesh = this.primitiveMeshMap.signatures.find(s => s.id === signature.id)?.mesh;
        // Prepare a group for role link lines
        const linkGroup = new THREE.Group();
        linkGroup.name = 'roles-links';
        this.scene.add(linkGroup);

        for (const [typeId, group] of byType.entries()) {
            const typeMesh = this.primitiveMeshMap.types.find(t => t.id === typeId)?.mesh;
            if (!typeMesh) continue;
            const center = typeMesh.position.clone();

            const roleIds = [...group.inputs, ...group.outputs];
            const entities: PrimitiveMetaJson[] = roleIds.map((rid) => ({ id: rid, name: rid } as unknown as PrimitiveMetaJson));
            const inputSet = new Set(group.inputs);

            // Compute a vertical plane basis that follows the formats ring xz-wise.
            // Use the parent format's tangent direction for the u-axis and world up for v-axis.
            const origin = new THREE.Vector3(0, 0, 0);
            const up = new THREE.Vector3(0, 1, 0);
            // Find the type's parent format center from metadata
            const typeMeta = this.typesMeta.find(t => t.id === typeId);
            const formatCenter = typeMeta ? this.primitiveMeshMap.formats.find(f => f.id === typeMeta.formatId)?.mesh.position.clone() : undefined;
            let uAxis: THREE.Vector3;
            const vAxis: THREE.Vector3 = up;
            if (formatCenter) {
                // Tangent at the format center w.r.t. the world origin
                const radialFormat = formatCenter.clone().sub(origin);
                // Project out any Y to get xz-plane radial
                const upCompFmt = up.clone().multiplyScalar(radialFormat.dot(up));
                radialFormat.sub(upCompFmt);
                if (radialFormat.lengthSq() < 1e-6) radialFormat.set(1, 0, 0);
                radialFormat.normalize();
                const tangentFormat = new THREE.Vector3().crossVectors(up, radialFormat);
                if (tangentFormat.lengthSq() < 1e-6) tangentFormat.set(0, 0, -1);
                uAxis = tangentFormat.normalize();
            } else {
                // Fallback: use radial from world origin to type center
                const radialXZ = center.clone().sub(origin);
                const upComp = up.clone().multiplyScalar(radialXZ.dot(up));
                radialXZ.sub(upComp); // xz projection
                if (radialXZ.lengthSq() < 1e-6) radialXZ.set(1, 0, 0);
                uAxis = radialXZ.normalize();
            }

            this.drawRingBasis(
                CONSTANTS.PRIMITIVES.roles,
                entities,
                center,
                meshConfigRoles,
                { u: uAxis, v: vAxis },
                {
                    // Follow the format ring: use explicit basis with u = tangent, v = up
                    orientationMode: 'given',
                    guideVisible: true,
                    guideColor: 0x44aa88,
                    // Use a 1px line loop for the guide to maximize visibility across GPUs
                    guideThickness: 0,
                    guideSegments: 128,
                    semicircleBy: (entity) => (inputSet.has(entity.id) ? 'input' : 'output'),
                    nameBy: (entity) => String((entity as unknown as { name?: unknown }).name ?? entity.id),
                }
            );

            // Build a quick lookup for the role meshes we just created
            const roleMeshById = new Map<string, THREE.Mesh>();
            for (const r of this.primitiveMeshMap.roles) roleMeshById.set(r.id, r.mesh);

            // Helper to create colored lines
            const makeLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
                const points = [from.clone(), to.clone()];
                const geom = new THREE.BufferGeometry().setFromPoints(points);
                const mat = new THREE.LineBasicMaterial({ color });
                const line = new THREE.Line(geom, mat);
                line.renderOrder = 999;
                return line;
            };

            // Draw lines from the signature to each role
            const sigPos = sigMesh?.position ?? new THREE.Vector3(0, 0, 0);
            for (const rid of group.inputs) {
                const rMesh = roleMeshById.get(rid);
                if (rMesh) linkGroup.add(makeLine(sigPos, rMesh.position, 0x00cc66));
            }
            for (const rid of group.outputs) {
                const rMesh = roleMeshById.get(rid);
                if (rMesh) linkGroup.add(makeLine(sigPos, rMesh.position, 0xcc0033));
            }
        }
    }

    private drawSignatures() {
        const signatures = this.signaturesMeta;
        if (!signatures || signatures.length === 0) {
            console.log('No signatures to draw.');
            return;
        }

        // Find the target format center
        const targetFormatId = CONSTANTS.SPECIALS.FORMAT_ApplicationJob;
        const targetFormat = this.primitiveMeshMap.formats.find((f) => f.id === targetFormatId)?.mesh;

        // We'll still use a group to hold connection lines (and for cleanup by name)
        const signatureGroup = new THREE.Group();
        signatureGroup.name = CONSTANTS.PRIMITIVES.signatures;
        this.scene.add(signatureGroup);

        // Determine ring center and radius
        const center = targetFormat ? targetFormat.position.clone() : new THREE.Vector3(0, 0, 0);
        const u = new THREE.Vector3(1, 0, 0);
        const v = new THREE.Vector3(0, 0, 1);
        const ringRadius: number = 'ringRadius' in meshConfigTypes.geometry
            ? (meshConfigTypes.geometry as SphereConfig).ringRadius
            : 26;

        // If global special toggles are enabled, place all signatures on a single half-arc
        // relative to the inward direction toward the origin. Otherwise, keep the existing
        // behavior of placing certain special signatures on the inner half and others on outer.
        if (this.config.specialTypeRing?.isSpecialEnabled) {
            const innerHalfCenter = this.computeInnerCenterAngle(center);
            const useInner = !!(this.config.specialTypeRing?.innerHemisphere);
            const centerAngle = useInner ? innerHalfCenter : innerHalfCenter + Math.PI;
            this.drawRingBasis(
                CONSTANTS.PRIMITIVES.signatures,
                signatures as unknown as PrimitiveMetaJson[],
                center,
                meshConfigSignatures,
                { u, v },
                {
                    orientationMode: 'given',
                    ringRadius,
                    angleBy: this.angleByHalfArc(centerAngle),
                    dummyBy: (entity) => String((entity as SignatureMetaJson).name).toLowerCase().includes('dummy'),
                    nameBy: (entity) => String((entity as SignatureMetaJson).name),
                }
            );
        } else {
            // Special behavior: two special signatures should point inward and be symmetric around the inward direction
            const specialSignatureAId = CONSTANTS.SPECIALS.SIGNATURE_Builder;
            const specialSignatureBId = CONSTANTS.SPECIALS.SIGNATURE_Engine;
            const specialFormatAId = CONSTANTS.SPECIALS.FORMAT_ApplicationJson;
            const specialFormatAMesh = this.primitiveMeshMap.formats.find((f) => f.id === specialFormatAId)?.mesh;

            // Compute inward angle from target format to special format A; fallback to 0 if not found
            let inwardAngle = 0;
            if (targetFormat && specialFormatAMesh) {
                const dir = specialFormatAMesh.position.clone().sub(center);
                const dirXZ = new THREE.Vector3(dir.x, 0, dir.z);
                if (dirXZ.lengthSq() > 1e-8) {
                    dirXZ.normalize();
                    inwardAngle = Math.atan2(dirXZ.z, dirXZ.x);
                }
            }

            // Build angle map placing specials on the inner semicircle (toward origin) and others on the outer semicircle (away from origin).
            // Define inner direction as pointing from the signatures ring center toward world origin.
            const angleMap = new Map<string, number>();
            const hasA = signatures.some((j) => j.id === specialSignatureAId);
            const hasB = signatures.some((j) => j.id === specialSignatureBId);
            const specials: SignatureMetaJson[] = [];
            if (hasA) specials.push(signatures.find(j => j.id === specialSignatureAId)!);
            if (hasB) specials.push(signatures.find(j => j.id === specialSignatureBId)!);
            const others = signatures.filter((j) => j.id !== specialSignatureAId && j.id !== specialSignatureBId);

            // Compute inner angle toward origin for half-arc centers
            const origin = new THREE.Vector3(0, 0, 0);
            const toOrigin = origin.clone().sub(center); // from ring center to origin
            const toOriginXZ = new THREE.Vector3(toOrigin.x, 0, toOrigin.z);
            let innerHalfCenter = 0; // radians on the xz-plane
            if (toOriginXZ.lengthSq() > 1e-8) {
                toOriginXZ.normalize();
                innerHalfCenter = Math.atan2(toOriginXZ.z, toOriginXZ.x);
            }
            const outerHalfCenter = innerHalfCenter + Math.PI;

            // Helper: distribute n items evenly across a half-arc centered at centerAngle
            const placeHalf = (ids: string[], centerAngle: number) => {
                const n = ids.length;
                if (n <= 0) return;
                for (let i = 0; i < n; i++) {
                    const angle = centerAngle - Math.PI / 2 + ((i + 0.5) / n) * Math.PI;
                    angleMap.set(ids[i], angle);
                }
            };

            // Place specials on inner half, in stable order [Builder, Engine] if both present
            placeHalf(specials.map(s => s.id), innerHalfCenter);
            // Place remaining on outer half
            placeHalf(others.map(s => s.id), outerHalfCenter);

            // Draw signatures on a ring using the generic helper. Supply ringRadius override and angles.
            this.drawRingBasis(
                CONSTANTS.PRIMITIVES.signatures,
                signatures as unknown as PrimitiveMetaJson[],
                center,
                meshConfigSignatures,
                { u, v },
                {
                    orientationMode: 'given',
                    ringRadius,
                    angleBy: (entity, index, count) => {
                        const id = (entity as SignatureMetaJson).id;
                        if (angleMap.has(id)) return angleMap.get(id)!;
                        // If no special signature present or as fallback, distribute evenly
                        return (index / count) * Math.PI * 2;
                    },
                    dummyBy: (entity) => String((entity as SignatureMetaJson).name).toLowerCase().includes('dummy'),
                    nameBy: (entity) => String((entity as SignatureMetaJson).name),
                }
            );
        }

        // Draw connection lines from each signature to its inputs/outputs roles (if present)
        const roleMeshById = new Map<string, THREE.Mesh>();
        for (const r of this.primitiveMeshMap.roles) roleMeshById.set(r.id, r.mesh);

        type SignatureRoles = { inputs?: Record<string, string>; outputs?: Record<string, string> };
        const makeLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            const points = [from.clone(), to.clone()];
            const geom = new THREE.BufferGeometry().setFromPoints(points);
            const mat = new THREE.LineBasicMaterial({ color });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 999;
            return line;
        };
        // Build a map signatureId -> mesh for lines
        const signatureMeshById = new Map<string, THREE.Mesh>();
        for (const j of this.primitiveMeshMap.signatures) signatureMeshById.set(j.id, j.mesh);

        for (const signature of signatures as SignatureMetaJson[]) {
            const mesh = signatureMeshById.get(signature.id);
            if (!mesh) continue;
            const roles = (signature as Partial<SignatureMetaJson> & { roles?: SignatureRoles })?.roles;
            const inputIds = Object.keys(roles?.inputs ?? {});
            const outputIds = Object.keys(roles?.outputs ?? {});
            // Debug: log linkage for this signature
            try {
                // console.log(`[Explorer] signature links ${signature.name} (${signature.id}):`, { inputs: inputs.length, outputs: outputs.length });
            } catch { }
            for (const id of inputIds) {
                const roleMesh = roleMeshById.get(id);
                if (roleMesh) signatureGroup.add(makeLine(mesh.position, roleMesh.position, 0x00cc66));
            }
            for (const id of outputIds) {
                const roleMesh = roleMeshById.get(id);
                if (roleMesh) signatureGroup.add(makeLine(mesh.position, roleMesh.position, 0xcc0033));
            }
        }
    }

    // Generic helper to draw small rings of external items around parent entities (types, signatures)
    // and route single straight lines from each parent to its ring center with collision-avoidance.
    private drawExternalItemsAroundParents<TItem>(opts: {
        groupName: string; // name for the group container (e.g., CONSTANTS.STORAGE.resources or CONSTANTS.PRIMITIVES.implementations)
        entityConst: string; // userData.entity tag
        parentEntries: { id: string; mesh: THREE.Mesh }[]; // parent meshes already in the scene
        itemsByParentId: Record<string, TItem[]>; // map parentId -> items
        // Radii and spacing
        baseRadiusFromOrigin: number; // base radius from world origin where rings are placed (will expand outward)
        perRingRadius: number; // radius of each small ring (around the chosen center)
        ringMinGap: number; // min spacing between ring discs
        interiorBarrierRadius: number; // forbid straight lines from entering this radial cylinder (format ring)
        yLift: number; // lift of ring centers in +Y
        // Item mesh factory; receives inward direction (from item to parent) for orientation
        createItemMesh: (item: TItem, inwardDir: THREE.Vector3) => THREE.Mesh;
        // Identity for userData
        getItemIdentity: (item: TItem) => { id: string; name: string };
        // Line color between parent and ring center
        lineColor: number;
    }) {
        const {
            groupName,
            entityConst,
            parentEntries,
            itemsByParentId,
            baseRadiusFromOrigin,
            perRingRadius,
            ringMinGap,
            interiorBarrierRadius,
            yLift,
            createItemMesh,
            getItemIdentity,
            lineColor,
        } = opts;

        if (!parentEntries || parentEntries.length === 0) return;

        const group = new THREE.Group();
        group.name = groupName;
        this.scene.add(group);

        // Prepare obstacles from existing meshes (formats, types, roles, signatures) to avoid crossings
        const obstacles: { center: THREE.Vector3; radius: number; mesh?: THREE.Mesh }[] = [];
        const getMeshBoundingRadius = (mesh: THREE.Mesh) => {
            const geom = mesh.geometry as THREE.BufferGeometry | undefined;
            if (!geom) return 0.5;
            if (!geom.boundingSphere) geom.computeBoundingSphere();
            const r = geom.boundingSphere?.radius ?? 0.5;
            const s: THREE.Vector3 = mesh.scale as THREE.Vector3;
            const maxScale = Math.max(Math.abs(s.x), Math.abs(s.y), Math.abs(s.z));
            return r * maxScale;
        };
        for (const f of this.primitiveMeshMap.formats) obstacles.push({ center: f.mesh.position.clone(), radius: getMeshBoundingRadius(f.mesh), mesh: f.mesh });
        for (const t of this.primitiveMeshMap.types) obstacles.push({ center: t.mesh.position.clone(), radius: getMeshBoundingRadius(t.mesh), mesh: t.mesh });
        for (const r of this.primitiveMeshMap.roles) obstacles.push({ center: r.mesh.position.clone(), radius: getMeshBoundingRadius(r.mesh), mesh: r.mesh });
        for (const j of this.primitiveMeshMap.signatures) obstacles.push({ center: j.mesh.position.clone(), radius: getMeshBoundingRadius(j.mesh), mesh: j.mesh });

        // Geometry helpers for collision tests
        const segmentPointDistance = (a: THREE.Vector3, b: THREE.Vector3, p: THREE.Vector3) => {
            const ab = b.clone().sub(a);
            const t = THREE.MathUtils.clamp(p.clone().sub(a).dot(ab) / ab.lengthSq(), 0, 1);
            const proj = a.clone().add(ab.multiplyScalar(t));
            return proj.distanceTo(p);
        };
        const segmentSegmentDistance = (p1: THREE.Vector3, q1: THREE.Vector3, p2: THREE.Vector3, q2: THREE.Vector3) => {
            const d1 = q1.clone().sub(p1);
            const d2 = q2.clone().sub(p2);
            const r = p1.clone().sub(p2);
            const a = d1.dot(d1);
            const e = d2.dot(d2);
            const f = d2.dot(r);
            let s: number, t: number;
            if (a <= 1e-8 && e <= 1e-8) return p1.distanceTo(p2);
            if (a <= 1e-8) { s = 0; t = THREE.MathUtils.clamp(f / e, 0, 1); }
            else {
                const c = d1.dot(r);
                if (e <= 1e-8) { t = 0; s = THREE.MathUtils.clamp(-c / a, 0, 1); }
                else {
                    const b = d1.dot(d2);
                    const denom = a * e - b * b;
                    if (denom !== 0) s = THREE.MathUtils.clamp((b * f - c * e) / denom, 0, 1); else s = 0;
                    const tNom = b * s + f;
                    if (tNom < 0) { t = 0; s = THREE.MathUtils.clamp(-c / a, 0, 1); }
                    else if (tNom > e) { t = 1; s = THREE.MathUtils.clamp((b - c) / a, 0, 1); }
                    else { t = tNom / e; }
                }
            }
            const c1 = p1.clone().add(d1.clone().multiplyScalar(s));
            const c2 = p2.clone().add(d2.clone().multiplyScalar(t));
            return c1.distanceTo(c2);
        };

        // Helpers
        const up = new THREE.Vector3(0, 1, 0);
        const rotateAroundY = (dir: THREE.Vector3, radians: number) => {
            const m = new THREE.Matrix4().makeRotationY(radians);
            return dir.clone().applyMatrix4(m).normalize();
        };
        const generateOffsets = (stepDeg: number, maxDeg: number) => {
            const res: number[] = [0];
            const step = THREE.MathUtils.degToRad(stepDeg);
            const max = THREE.MathUtils.degToRad(maxDeg);
            for (let k = 1; k <= Math.floor(max / step); k++) { const a = k * step; res.push(+a, -a); }
            return res;
        };

        const placedCenters: { parentId: string; center: THREE.Vector3 }[] = [];
        const placedSegments: { a: THREE.Vector3; b: THREE.Vector3 }[] = [];

        // Iterate parents deterministically in the order meshes were created
        for (const parent of parentEntries) {
            const parentId = parent.id;
            const items = itemsByParentId[parentId];
            if (!items || items.length === 0) continue;

            const parentPos = parent.mesh.position.clone();
            const radial = new THREE.Vector3(parentPos.x, 0, parentPos.z);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
            radial.normalize();

            // Place ring center by scanning angles and expanding outward until clear
            const minCenterDistance = 2 * perRingRadius + ringMinGap;
            const offsets = generateOffsets(12, 180);
            const maxExpansionSteps = 6;
            const expansionStep = perRingRadius + 0.5;
            let chosenCenter: THREE.Vector3 | null = null;
            outerSearch:
            for (let expand = 0; expand <= maxExpansionSteps; expand++) {
                const radius = baseRadiusFromOrigin + expand * expansionStep;
                for (const off of offsets) {
                    const dirXZ = rotateAroundY(radial, off);
                    const candidate = dirXZ.clone().multiplyScalar(radius).add(new THREE.Vector3(0, yLift, 0));
                    // Ensure this center isn't colliding with previously placed centers
                    let ok = true;
                    for (const p of placedCenters) { if (candidate.distanceTo(p.center) < minCenterDistance) { ok = false; break; } }
                    if (!ok) continue;
                    // Forbid line to pass inside the main format ring interior (keep visuals outside)
                    const samples = 12;
                    for (let sIdx = 0; sIdx <= samples; sIdx++) {
                        const t = sIdx / samples;
                        const pos = parentPos.clone().lerp(candidate, t);
                        const rXZ = Math.hypot(pos.x, pos.z);
                        if (rXZ < interiorBarrierRadius + 0.05) { ok = false; break; }
                    }
                    if (!ok) continue;
                    // Avoid entity obstacles
                    const lineA = parentPos; const lineB = candidate; const lineClearance = 0.2;
                    for (const ob of obstacles) {
                        if (ob.center.distanceTo(parentPos) < 1e-6) continue; // skip self
                        const dist = segmentPointDistance(lineA, lineB, ob.center);
                        if (dist < ob.radius + lineClearance) { ok = false; break; }
                    }
                    if (!ok) continue;
                    // Avoid collisions with prior rings and lines
                    for (const p of placedCenters) { const dist = segmentPointDistance(lineA, lineB, p.center); if (dist < perRingRadius + ringMinGap) { ok = false; break; } }
                    if (!ok) continue;
                    const lineGap = 0.2;
                    for (const seg of placedSegments) { const d = segmentSegmentDistance(lineA, lineB, seg.a, seg.b); if (d < lineGap) { ok = false; break; } }
                    if (!ok) continue;
                    chosenCenter = candidate; break outerSearch;
                }
            }
            if (!chosenCenter) chosenCenter = radial.clone().multiplyScalar(baseRadiusFromOrigin).add(new THREE.Vector3(0, yLift, 0));

            placedCenters.push({ parentId, center: chosenCenter.clone() });

            // Basis for the small ring: make u point toward parent from ring center
            const towardParentFromCenter = parentPos.clone().sub(chosenCenter).normalize();
            const normal = new THREE.Vector3(0, 1, 0); // align to world-xz by default
            const proj = towardParentFromCenter.clone().sub(normal.clone().multiplyScalar(towardParentFromCenter.dot(normal)));
            const uAxis = (proj.lengthSq() < 1e-6 ? computeBasisFromNormal(normal).u : proj.normalize());
            const vAxis = new THREE.Vector3().crossVectors(normal, uAxis).normalize();

            const n = items.length;
            // Guide ring for this parent
            this.drawRingGuideBasis(groupName, chosenCenter, perRingRadius, n, { u: uAxis, v: vAxis }, { guideColor: 0x999999, guideSegments: Math.max(48, n * 12), guideThickness: 0, orientationMode: 'given' });

            // Place items on the small ring and draw a single line for the parent
            // First, draw the link line from parent to ring center
            const linePoints = [parentPos.clone(), chosenCenter.clone()];
            const lineGeom = new THREE.BufferGeometry().setFromPoints(linePoints);
            const lineMat = new THREE.LineBasicMaterial({ color: lineColor });
            const guideLine = new THREE.Line(lineGeom, lineMat);
            guideLine.renderOrder = 998;
            group.add(guideLine);
            placedSegments.push({ a: parentPos.clone(), b: chosenCenter.clone() });

            // Then the items themselves
            for (let j = 0; j < n; j++) {
                const item = items[j];
                const phi = (j / n) * Math.PI * 2;
                const pos = chosenCenter.clone()
                    .add(uAxis.clone().multiplyScalar(Math.cos(phi) * perRingRadius))
                    .add(vAxis.clone().multiplyScalar(Math.sin(phi) * perRingRadius));

                const inward = parentPos.clone().sub(pos).normalize();
                const mesh = createItemMesh(item, inward);
                mesh.position.copy(pos);
                const ident = getItemIdentity(item);
                mesh.userData = { entity: entityConst, id: ident.id, name: ident.name };
                group.add(mesh);
            }
        }
    }

    // Compute the angle (radians) of the in-plane inward direction from a ring center toward the world origin.
    private computeInnerCenterAngle(center: THREE.Vector3, origin = new THREE.Vector3(0, 0, 0)): number {
        const toOrigin = origin.clone().sub(center);
        const toOriginXZ = new THREE.Vector3(toOrigin.x, 0, toOrigin.z);
        if (toOriginXZ.lengthSq() > 1e-8) {
            toOriginXZ.normalize();
            return Math.atan2(toOriginXZ.z, toOriginXZ.x);
        }
        return 0;
    }

    // Provide a half-arc angleBy function centered at centerAngle.
    private angleByHalfArc(centerAngle: number) {
        return (_entity: PrimitiveMetaJson, i: number, count: number) => centerAngle - Math.PI / 2 + ((i + 0.5) / count) * Math.PI;
    }

    // Compute a base radius that safely encloses the inner framework (formats, types, roles, signatures)
    private getFrameworkBaseRadius(): number {
        const formatRingRadius: number = 'ringRadius' in meshConfigFormats.geometry ? (meshConfigFormats.geometry as SphereConfig).ringRadius : 20;
        const typesRingRadius: number = 'ringRadius' in meshConfigTypes.geometry ? (meshConfigTypes.geometry as SphereConfig).ringRadius : 6;
        const rolesRingRadius: number = 'ringRadius' in meshConfigRoles.geometry ? (meshConfigRoles.geometry as SphereConfig).ringRadius : 4;
        const signaturesRingRadius: number = 'ringRadius' in meshConfigTypes.geometry ? (meshConfigTypes.geometry as SphereConfig).ringRadius : 26;
        const outerMargin = 8;
        const innerMax = Math.max(typesRingRadius + rolesRingRadius, typesRingRadius + signaturesRingRadius);
        return formatRingRadius + innerMax + outerMargin;
    }

    // Ensure a single shared translucent sphere exists at the given radius; update if radius changed
    private ensureExternalSphere(radius: number) {
        // Only draw if outer-sphere mode is active
        const useOuterSphereForExternal = !!(this.config.specialTypeRing?.isSpecialEnabled) && !this.config.specialTypeRing?.innerHemisphere;
        if (!useOuterSphereForExternal) return;

        let existing: THREE.Mesh | undefined;
        this.scene.traverse((child) => {
            if (child.name === 'external-sphere-guide') existing = child as THREE.Mesh;
        });

        const opacity = this.config.externalSphere?.opacity ?? 0.07;
        if (existing) {
            const current = (existing.userData?.radius as number | undefined) ?? 0;
            if (Math.abs(current - radius) < 1e-3) return; // up-to-date
            existing.parent?.remove(existing);
        }
        const geom = new THREE.SphereGeometry(radius, 80, 80);
        const mat = new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity, depthWrite: false, depthTest: false, side: THREE.DoubleSide });
        const sphere = new THREE.Mesh(geom, mat);
        sphere.name = 'external-sphere-guide';
        sphere.userData = { radius };
        sphere.renderOrder = 9998;
        this.scene.add(sphere);
    }

    private drawResources() {
        // Draw resources as inward-pointing cones outside the format circle.
        // For each type, place one resource ring and route a single diagonal straight line
        // from the type to the ring center. Ensure rings do not overlap by scanning
        // angles and expanding outward until clear.
        const resourcesByType = this.resourcesMerged;
        if (!resourcesByType) return;

        // Shared base radius for enclosing the framework
        const sharedBase = this.getFrameworkBaseRadius();

        const resourceRingRadius = 2.0;
        // const ringMinGap = 0.6; // legacy ring layout
        // const yLift = rolesRingRadius + 2; // legacy ring layout

        // Prebuild resource cone geometry
        const coneGeom = new THREE.ConeGeometry(0.3, 0.9, 16);
        const coneMat = new THREE.MeshStandardMaterial({ color: 'pink', metalness: 0.2, roughness: 0.8 });

        // Use outer-sphere placement when specialTypeRing toggle is enabled AND innerHemisphere is false.
        const useOuterSphereForExternal = !!(this.config.specialTypeRing?.isSpecialEnabled) && !this.config.specialTypeRing?.innerHemisphere;
        if (useOuterSphereForExternal) {
            const sharedRadius = sharedBase + (this.config.externalSphere?.radiusOffset ?? 16);
            this.ensureExternalSphere(sharedRadius);

            const workflowSpecTypeId = CONSTANTS.SPECIALS.TYPE_WorkflowSpec;
            const wfResources = resourcesByType?.[workflowSpecTypeId] ?? [];
            // Find the latest WorkflowSpec resource by timestamp
            let latestWfResource: typeof wfResources[0] | undefined = undefined;
            if (wfResources.length > 0) {
                latestWfResource = wfResources.reduce((latest, curr) => {
                    if (!latest) return curr;
                    const t1 = Date.parse(latest.timestamp ?? '');
                    const t2 = Date.parse(curr.timestamp ?? '');
                    return t2 > t1 ? curr : latest;
                }, undefined as typeof wfResources[0] | undefined);
            }
            // Extract references (implementations, signatures, resource paths, type ids) from the latest WorkflowSpec exposedData
            interface WfStepLike { execution?: { implementationId?: string } }
            interface WfLike { workflow?: { steps?: WfStepLike[] }; resourceMaps?: Array<Record<string, { path?: string }>> }
            const referencedTypeIds = new Set<string>();
            const referencedImplIds = new Set<string>();
            const referencedSigIds = new Set<string>();
            const referencedResourcePaths = new Set<string>();
            if (latestWfResource) {
                try {
                    const wfExposed = (latestWfResource as unknown as { exposedData?: unknown })?.exposedData as unknown as Partial<WfLike>;
                    const steps: WfStepLike[] = wfExposed?.workflow?.steps ?? [];
                    for (const s of steps) {
                        const implId: string | undefined = s?.execution?.implementationId;
                        if (implId) referencedImplIds.add(implId);
                    }
                    if (this.implementationsMeta && this.implementationsMeta.length > 0) {
                        for (const implId of referencedImplIds) {
                            const impl = this.implementationsMeta.find(i => i.id === implId);
                            const sid = impl?.signatureId;
                            if (sid) referencedSigIds.add(sid);
                        }
                    }
                    const rm0 = Array.isArray(wfExposed?.resourceMaps) ? wfExposed.resourceMaps[0] : undefined;
                    if (rm0 && typeof rm0 === 'object') {
                        for (const key of Object.keys(rm0)) {
                            const entry = rm0[key] as { path?: string } | undefined;
                            const p: string | undefined = entry?.path;
                            if (p && typeof p === 'string') {
                                referencedResourcePaths.add(p);
                                const m = p.match(/^(TYPE-[^/]+)\//);
                                if (m && m[1]) referencedTypeIds.add(m[1]);
                            }
                        }
                    }
                } catch { /* ignore parse issues; fall back to whatever we gathered */ }
            }

            // Exclude WorkflowSpec resources from the outer sphere cones
            const filteredByType: typeof resourcesByType = { ...(resourcesByType as Record<string, ResourceMerged[]>) };
            if (filteredByType[workflowSpecTypeId]) filteredByType[workflowSpecTypeId] = [];

            this.drawExternalItemsOnSphere<ResourceMerged>({
                groupName: CONSTANTS.STORAGE.resources,
                entityConst: CONSTANTS.STORAGE.resources,
                parentEntries: this.primitiveMeshMap.types.map(t => ({ id: t.id, mesh: t.mesh })),
                itemsByParentId: filteredByType,
                sphereRadius: sharedRadius,
                perClusterRadius: resourceRingRadius,
                createItemMesh: (_res, inwardDir) => {
                    const mesh = new THREE.Mesh(coneGeom, coneMat.clone());
                    mesh.quaternion.setFromUnitVectors(new THREE.Vector3(0, 1, 0), inwardDir);
                    mesh.castShadow = true; mesh.receiveShadow = true;
                    return mesh;
                },
                getItemIdentity: (res) => ({ id: res.path, name: String(res.exposedData?.semanticIdentity ?? '') }),
                lineColor: 0xffffff,
            });

            // Now that resource cones exist, draw the central WorkflowSpec box and black links to referenced entities
            if (wfResources.length > 0) {
                const wfGroup = new THREE.Group();
                wfGroup.name = CONSTANTS.STORAGE.resources;
                const boxGeom = new THREE.BoxGeometry(2.2, 2.2, 2.2);
                const boxMat = new THREE.MeshStandardMaterial({ color: 0x999999, metalness: 0.25, roughness: 0.8 });
                const centerBox = new THREE.Mesh(boxGeom, boxMat);
                centerBox.position.set(0, 0, 0);
                centerBox.castShadow = true; centerBox.receiveShadow = true;
                centerBox.userData = { entity: CONSTANTS.STORAGE.resources, id: `${workflowSpecTypeId}::central`, name: 'WorkflowSpec' };
                wfGroup.add(centerBox);

                const wsStyles = this.config.links?.workflowSpecConnections;
                const extStyles = this.config.links?.externalConnections;
                const lineOpacity = (wsStyles?.opacity ?? extStyles?.opacity ?? 0.25);
                const lineWidth = (wsStyles?.lineWidth ?? wsStyles?.linewidth ?? extStyles?.linewidth ?? 1);
                const makeBlackLine = (from: THREE.Vector3, to: THREE.Vector3) => {
                    const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
                    const mat = new THREE.LineBasicMaterial({ color: 0x000000, transparent: lineOpacity < 1, opacity: lineOpacity, depthWrite: false, depthTest: false, linewidth: lineWidth });
                    const line = new THREE.Line(geom, mat);
                    line.renderOrder = 998;
                    return line;
                };
                const center = new THREE.Vector3(0, 0, 0);

                // Helper lookups for meshes
                const typeMeshById = new Map<string, THREE.Mesh>();
                for (const t of this.primitiveMeshMap.types) typeMeshById.set(t.id, t.mesh);
                const sigMeshById = new Map<string, THREE.Mesh>();
                for (const s of this.primitiveMeshMap.signatures) sigMeshById.set(s.id, s.mesh);
                const findImplementationMesh = (implId: string): THREE.Mesh | null => {
                    let found: THREE.Mesh | null = null;
                    this.scene.traverse((obj: THREE.Object3D) => {
                        if (found) return;
                        const m = obj as THREE.Mesh;
                        if ((m as unknown as { isMesh?: boolean }).isMesh && m.userData?.entity === CONSTANTS.PRIMITIVES.implementations && m.userData?.id === implId) {
                            found = m;
                        }
                    });
                    return found;
                };
                const findResourceMeshByPath = (path: string): THREE.Mesh | null => {
                    let found: THREE.Mesh | null = null;
                    this.scene.traverse((obj: THREE.Object3D) => {
                        if (found) return;
                        const m = obj as THREE.Mesh;
                        if ((m as unknown as { isMesh?: boolean }).isMesh && m.userData?.entity === CONSTANTS.STORAGE.resources && m.userData?.id === path) {
                            found = m;
                        }
                    });
                    return found;
                };

                const wsToggles = this.config.links?.workflowSpecConnections;
                const drawToResources = wsToggles?.toResources ?? true;
                const drawToTypes = wsToggles?.toTypes ?? true;
                const drawToSignatures = wsToggles?.toSignatures ?? true;
                const drawToImplementations = wsToggles?.toImplementations ?? true;

                // Lines to referenced resources (by path)
                if (drawToResources) {
                    for (const rp of referencedResourcePaths) {
                        const rm = findResourceMeshByPath(rp);
                        if (rm) wfGroup.add(makeBlackLine(center, rm.position));
                    }
                }
                // Lines to referenced Types (keep for context)
                if (drawToTypes) {
                    for (const tid of referencedTypeIds) {
                        const tm = typeMeshById.get(tid);
                        if (tm) wfGroup.add(makeBlackLine(center, tm.position));
                    }
                }
                // Lines to referenced Signatures
                if (drawToSignatures) {
                    for (const sid of referencedSigIds) {
                        const sm = sigMeshById.get(sid);
                        if (sm) wfGroup.add(makeBlackLine(center, sm.position));
                    }
                }
                // Lines to referenced Implementations
                if (drawToImplementations) {
                    for (const iid of referencedImplIds) {
                        const im = findImplementationMesh(iid);
                        if (im) wfGroup.add(makeBlackLine(center, im.position));
                    }
                }
                this.scene.add(wfGroup);
            }
        }
    }

    // Draw external items grouped by parent on an enclosing sphere surface.
    private drawExternalItemsOnSphere<TItem>(opts: {
        groupName: string;
        entityConst: string;
        parentEntries: { id: string; mesh: THREE.Mesh }[];
        itemsByParentId: Record<string, TItem[]>;
        sphereRadius: number; // radius of the enclosing sphere
        perClusterRadius: number; // angular spread proxy for cluster; used as tangent perturbation magnitude
        useUniformDistribution?: boolean; // if true, use spherical Fibonacci slots per parent; else follow parent direction
        createItemMesh: (item: TItem, inwardDir: THREE.Vector3) => THREE.Mesh;
        getItemIdentity: (item: TItem) => { id: string; name: string };
        lineColor: number;
    }) {
        const { groupName, entityConst, parentEntries, itemsByParentId, sphereRadius, perClusterRadius, useUniformDistribution = true, createItemMesh, getItemIdentity, lineColor } = opts;
        if (!parentEntries || parentEntries.length === 0) return;

        const group = new THREE.Group();
        group.name = groupName;
        this.scene.add(group);

        const makeLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
            const lineOpacity = this.config.links?.externalConnections?.opacity ?? 0.25;
            const lineWidth = this.config.links?.externalConnections?.linewidth ?? 1;
            const mat = new THREE.LineBasicMaterial({ color, transparent: lineOpacity < 1, opacity: lineOpacity, depthWrite: false, linewidth: lineWidth });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 998;
            return line;
        };

        // Build groups: only parents that actually have items
        const parentsWithItems = parentEntries.filter(p => (itemsByParentId[p.id] ?? []).length > 0);
        const totalGroups = parentsWithItems.length;

        // Generate uniformly-spaced directions on a unit sphere using spherical Fibonacci sequence
        const goldenAngle = Math.PI * (3 - Math.sqrt(5));
        const slotDirs: THREE.Vector3[] = [];
        if (useUniformDistribution && totalGroups > 0) {
            for (let i = 0; i < totalGroups; i++) {
                const k = i + 0.5;
                const y = 1 - (2 * k) / totalGroups; // y in [-1, 1]
                const r = Math.sqrt(Math.max(0, 1 - y * y));
                const theta = i * goldenAngle;
                const x = Math.cos(theta) * r;
                const z = Math.sin(theta) * r;
                slotDirs.push(new THREE.Vector3(x, y, z).normalize());
            }
        }

        let gi = 0;
        for (const parent of parentsWithItems) {
            const items = itemsByParentId[parent.id];
            if (!items || items.length === 0) continue;

            const parentPos = parent.mesh.position.clone();

            // Determine the slot direction for this group's cluster center
            let dir: THREE.Vector3;
            if (useUniformDistribution && slotDirs.length === totalGroups) {
                dir = slotDirs[gi++]?.clone() ?? new THREE.Vector3(1, 0, 0);
            } else {
                // Fallback: use normalized parent direction from origin
                dir = parentPos.clone().normalize();
                if (dir.lengthSq() < 1e-6) dir.set(1, 0, 0);
            }
            const clusterCenter = dir.clone().multiplyScalar(sphereRadius);

            // Draw line from parent to cluster center
            group.add(makeLine(parentPos, clusterCenter, lineColor));

            // Place items on the sphere near the slot direction using small angular offsets on the tangent plane
            const basis = computeBasisFromNormal(dir);
            const u = basis.u; const v = basis.v;
            const n = items.length;
            for (let i = 0; i < n; i++) {
                if (n === 1) {
                    // Exactly on the slot direction
                    const pos = clusterCenter.clone();
                    const inward = parentPos.clone().sub(pos).normalize();
                    const mesh = createItemMesh(items[i], inward);
                    mesh.position.copy(pos);
                    const ident = getItemIdentity(items[i]);
                    mesh.userData = { entity: entityConst, id: ident.id, name: ident.name };
                    group.add(mesh);
                    continue;
                }
                const phi = (i / n) * Math.PI * 2;
                // Tangent offset magnitude in angular terms; keep small so items stay near the slot
                const angularMag = Math.min(0.08, Math.max(0.02, perClusterRadius / Math.max(1, sphereRadius))); // ~1-5 degrees
                const tangent = u.clone().multiplyScalar(Math.cos(phi) * angularMag)
                    .add(v.clone().multiplyScalar(Math.sin(phi) * angularMag));
                const dirPerturbed = dir.clone().add(tangent).normalize();
                const pos = dirPerturbed.multiplyScalar(sphereRadius);

                const inward = parentPos.clone().sub(pos).normalize();
                const mesh = createItemMesh(items[i], inward);
                mesh.position.copy(pos);
                const ident = getItemIdentity(items[i]);
                mesh.userData = { entity: entityConst, id: ident.id, name: ident.name };
                group.add(mesh);
            }
        }
    }

    private drawImplementations() {
        const implementations = this.implementationsMeta;
        if (!implementations || implementations.length === 0) return;

        // Group implementations by signatureId
        const itemsBySignature: Record<string, ImplementationMetaJson[]> = {};
        for (const impl of implementations) {
            const sid = (impl as ImplementationMetaJson).signatureId;
            if (!sid) continue;
            if (!itemsBySignature[sid]) itemsBySignature[sid] = [];
            itemsBySignature[sid].push(impl);
        }

        // Shared base radius for enclosing the framework
        const sharedBase = this.getFrameworkBaseRadius();

        const implRingRadius = 2.0;
        // const ringMinGap = 0.6; // legacy ring layout
        // const yLift = 2 + 2; // legacy ring layout

        // Prebuild a cone for implementations; use material from meshConfigImplementations
        const coneGeom = new THREE.ConeGeometry(0.32, 1.0, 16);
        const coneMat = new THREE.MeshStandardMaterial({
            color: meshConfigImplementations.material.color,
            metalness: meshConfigImplementations.material.metalness,
            roughness: meshConfigImplementations.material.roughness,
            emissive: meshConfigImplementations.material.emissive,
        });

        const useOuterSphereForExternal = !!(this.config.specialTypeRing?.isSpecialEnabled) && !this.config.specialTypeRing?.innerHemisphere;
        if (useOuterSphereForExternal) {
            const sharedRadius = sharedBase + (this.config.externalSphere?.radiusOffset ?? 16);
            this.ensureExternalSphere(sharedRadius);
            this.drawExternalItemsOnSphere<ImplementationMetaJson>({
                groupName: CONSTANTS.PRIMITIVES.implementations,
                entityConst: CONSTANTS.PRIMITIVES.implementations,
                parentEntries: this.primitiveMeshMap.signatures.map(s => ({ id: s.id, mesh: s.mesh })),
                itemsByParentId: itemsBySignature,
                sphereRadius: sharedRadius,
                perClusterRadius: implRingRadius,
                createItemMesh: (_impl, inwardDir) => {
                    const mesh = new THREE.Mesh(coneGeom, coneMat.clone());
                    mesh.quaternion.setFromUnitVectors(new THREE.Vector3(0, 1, 0), inwardDir);
                    mesh.castShadow = true; mesh.receiveShadow = true;
                    return mesh;
                },
                getItemIdentity: (impl) => ({ id: impl.id, name: String(impl.name ?? impl.id) }),
                lineColor: 0xffffff,
            });
        }
    }

    // Update primitives/resources and rebuild the scene without recreating the renderer/world
    updateData(formatsMeta: FormatMetaJson[], typesMeta: TypeMetaJson[], signaturesMeta: SignatureMetaJson[], implementationsMeta: ImplementationMetaJson[], resourcesMerged: Record<string, ResourceMerged[]>) {
        this.formatsMeta = formatsMeta;
        this.typesMeta = typesMeta;
        this.signaturesMeta = signaturesMeta;
        this.implementationsMeta = implementationsMeta;
        this.resourcesMerged = resourcesMerged;
        this.drawScene();
    }

    dispose(): void {
        try {
            // Stop render loop
            this.renderer.setAnimationLoop(null);
        } catch { }
        try {
            // Detach renderer canvas
            if (this.renderer?.domElement && this.renderer.domElement.parentElement) {
                this.renderer.domElement.parentElement.removeChild(this.renderer.domElement);
            }
        } catch { }
        try {
            // Dispose renderer and scene resources
            this.scene.traverse((obj: THREE.Object3D) => {
                const mesh = obj as THREE.Mesh;
                if (mesh && (mesh as unknown as { isMesh?: boolean }).isMesh) {
                    const geom = mesh.geometry as THREE.BufferGeometry | undefined;
                    const mat = mesh.material as THREE.Material | THREE.Material[] | undefined;
                    if (geom) geom.dispose?.();
                    if (Array.isArray(mat)) mat.forEach(m => m.dispose?.()); else mat?.dispose?.();
                }
            });
            this.renderer.dispose?.();
        } catch { }
    }

}