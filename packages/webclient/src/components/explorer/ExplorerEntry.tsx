'use client';
import ExplorerDataFetcher from './ExplorerDataFetcher';
import { TransientSelector, PersistentSelector } from './_lib/interactors/selectors';
import { SwitchingInteractor } from './_lib/interactors/SwitchingInteractor';
import * as THREE from 'three';
import { makeExplorerConfig } from './_lib/config';
import { useCallback, useMemo, memo } from 'react';


const MemoExplorerDataFetcher = memo(ExplorerDataFetcher);

export default function ExplorerEntry() {

    // Stable function/object identities to avoid re-renders causing XR jank
    const predicate = useCallback((obj: THREE.Object3D) => {
        const isTarget = true;
        if (isTarget) {
            // console.log('Predicate found target:', obj.name, obj);
        }
        return isTarget;
    }, []);

    const selector = useMemo(() => new PersistentSelector(), []);

    const interactorFactory = useCallback(
        (
            explorer: ConstructorParameters<typeof SwitchingInteractor>[0],
            selectorInstance: ConstructorParameters<typeof SwitchingInteractor>[1]
        ) => new SwitchingInteractor(explorer, selectorInstance),
        []
    );

    const explorerConfig = useMemo(() =>
        makeExplorerConfig(
            { predicate, selector, interactorFactory },
            {
                // Optional per-page overrides go here
                // formatTest0TypesInnerHemisphere: false, // already default in baseExplorerConfig
            }
        ),
        [predicate, selector, interactorFactory]
    );

    return (
        <div>
            <MemoExplorerDataFetcher {...explorerConfig} />
        </div>
    );

}
