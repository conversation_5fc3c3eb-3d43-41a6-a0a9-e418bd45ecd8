import type { <PERSON><PERSON><PERSON>, Imple<PERSON><PERSON><PERSON><PERSON><PERSON>, Exec<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, StepId<PERSON><PERSON>, ResourceMapJson, ResourceIdJson, BranchStepJson, ResourceMetaActiveJson } from '@toolproof/_schemas';
import type { SelectedIndex } from '@/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import ResourceMapsPanel from '@/components/builders/workflow/ResourceMapsPanel';
import CircleBadge from '@/components/builders/workflow/CircleBadge';
import type { DragSource } from '@/components/builders/workflow/ResourceMapsPanel';
import { useMemo, useRef, useState, useEffect } from 'react';

interface ResourceBindingsPanelProps {
    activeStep: StepJson;
    bindingOwner: ExecutionJson;
    selectedIndex: SelectedIndex;
    implementationMap: Map<string, ImplementationMetaJson>;
    resourceMap: ResourceMapJson;
    resources: ResourceMetaActiveJson[];
    onBindInputPath: (roleId: string, resource: ResourceMetaActiveJson) => void;
    onBindInputPointer: (roleId: string, filePath: string) => void;
    onClearInputBinding: (resourceId: ResourceIdJson) => void;
}

export default function ResourceBindingsPanel({ activeStep, bindingOwner, selectedIndex, implementationMap, resources, resourceMap, onBindInputPath, onBindInputPointer, onClearInputBinding }: ResourceBindingsPanelProps) {
    const stepKind = activeStep.kind;
    const activeImplementation = implementationMap.get(bindingOwner.implementationId) || null; // ATTENTION: Can't we just pass activeImplementation as a prop?

    // Build a local role map from the active implementation's roles (inputs and outputs)
    const roleMap = useMemo(() => {
        const map = new Map<string, RoleJson>();
        if (!activeImplementation) return map;
        const inObj = activeImplementation.roles?.inputs ?? {};
        const outObj = activeImplementation.roles?.outputs ?? {};
        Object.entries(inObj).forEach(([id, role]) => map.set(id, { id, ...role } as RoleJson));
        Object.entries(outObj).forEach(([id, role]) => map.set(id, { id, ...role } as RoleJson));
        return map;
    }, [activeImplementation]);

    // Compute exclude list for branch sibling cases
    let excludeCaseStepIds: string[] = [];
    if (stepKind === CONSTANTS.STEP.branch) {
        const cases = (activeStep as BranchStepJson).cases ?? [];
        const rawIdx = selectedIndex?.caseIndex ?? 0;
        const caseIdx = Math.max(0, Math.min(rawIdx, Math.max(cases.length - 1, 0)));
        excludeCaseStepIds = cases
            .map(cw => (cw?.what?.id as StepIdJson | undefined))
            .filter((id, idx): id is StepIdJson => Boolean(id) && idx !== caseIdx);
    }

    const inputRoles = useMemo(() => {
        if (!activeImplementation) return [] as RoleJson[];
        const inputsObj = activeImplementation.roles?.inputs ?? {};
        return Object.entries(inputsObj).map(([id, role]) => ({ id, ...role } as RoleJson));
    }, [activeImplementation]);

    const outputRoles = useMemo(() => {
        if (!activeImplementation) return [] as RoleJson[];
        const outputsObj = activeImplementation.roles?.outputs ?? {};
        return Object.entries(outputsObj).map(([id, role]) => ({ id, ...role } as RoleJson));
    }, [activeImplementation]);

    // Simple drag state for connecting earlier inputs/outputs to current inputs
    const [dragSource, setDragSource] = useState<DragSource | null>(null);
    const handleDropOnInput = (roleId: string, roleName: string, _e?: DragEvent | React.DragEvent<Element>) => {
        const source: DragSource | null = dragSource ?? null;
        console.log('handleDropOnInput', { roleId, roleName, source, selectedIndex });

        if (source != null) {
            // Only allow drop when typeId matches
            const inputRole = inputRoles.find(r => r.id === roleId);
            const inputType = inputRole?.typeId;
            if (inputType && source.typeId && inputType !== source.typeId) {
                // types don't match; ignore drop
                setDragSource(null);
                return;
            }
            // Accept the drop when types match; ResourceMapsPanel filters sources
            onBindInputPointer(roleId, source.bindingKey);
            setDragSource(null);
        }
    };

    const [pickerRole, setPickerRole] = useState<string | null>(null);
    // For WhileStep: which role id is designated as the iterating input
    const [iteratingRoleId, setIteratingRoleId] = useState<string | null>(null);

    // reset iterating selection when step changes
    useEffect(() => {
        setIteratingRoleId(null); // ATTENTION
    }, [activeStep?.id]);

    // Close picker when clicking anywhere in the panel outside the picker trigger/menu
    const panelBodyRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        const onDocClick = (e: MouseEvent) => {
            // If click is outside the panel body, just close; if inside, we'll rely on stopPropagation from buttons/menus
            if (panelBodyRef.current && !panelBodyRef.current.contains(e.target as Node)) {
                setPickerRole(null);
            }
        };
        // Listen for custom drag events from output badges
        const onCustomDrag = (e: Event) => {
            const ce = e as CustomEvent;
            setDragSource(ce.detail as DragSource);
        };
        const onCustomDragEnd = () => setDragSource(null);
        document.addEventListener('toolproof-drag-source', onCustomDrag as EventListener);
        document.addEventListener('toolproof-drag-end', onCustomDragEnd as EventListener);
        document.addEventListener('mousedown', onDocClick);
        return () => {
            document.removeEventListener('mousedown', onDocClick);
            document.removeEventListener('toolproof-drag-source', onCustomDrag as EventListener);
            document.removeEventListener('toolproof-drag-end', onCustomDragEnd as EventListener);
        };
    }, []);

    // console.log('activeImplementation', JSON.stringify(activeImplementation, null, 2));
    // console.log('bindingOwner', JSON.stringify(bindingOwner, null, 2));

    return (
        <div className="h-full flex flex-col bg-red-100">
            <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Resources</h2>
                <p className="text-xs text-gray-500">Expected inputs for the selected step.</p>
            </div>

            <div className="flex-1 p-4 select-none overflow-auto" ref={panelBodyRef} onClick={() => setPickerRole(null)}>

                <div className="space-y-4">
                    <div className="text-sm text-gray-800 font-medium mb-2">Step: {activeStep.id || '(unknown)'}{stepKind ? ` (${stepKind})` : ''}</div>

                    { /* ResourceMapsPanel for drag-and-drop functionality wrt. previous resources */}
                    <ResourceMapsPanel
                        bindingOwner={bindingOwner}
                        roleMap={roleMap}
                        resourceMap={resourceMap}
                        setDragSource={setDragSource}
                        excludeStepIds={excludeCaseStepIds}
                    />

                    {/* Current step inputs */}
                    <div>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Inputs</div>
                        <div className="flex flex-wrap gap-3">
                            {inputRoles.length === 0 ? (
                                <div className="text-sm text-gray-500">No inputs</div>
                            ) : (
                                inputRoles.map((input, i) => {
                                    const inputBindingId = bindingOwner.resourceBindings?.inputBindings?.[input.id] as string | undefined;
                                    const isBound = !!inputBindingId && inputBindingId in (resourceMap || {});
                                    const circleCls = isBound
                                        ? 'bg-green-500 text-white border-green-600'
                                        : 'bg-white text-green-600 border-green-500 hover:bg-green-50';
                                    let val: string = '<UNBOUND>';
                                    if (isBound) {
                                        const entry = (resourceMap[inputBindingId] as ResourceMetaActiveJson | undefined) ?? undefined;
                                        const semId = (entry?.exposedData as any)?.semanticIdentity as string | undefined;
                                        val = semId || '<UNBOUND>';
                                    }

                                    return (
                                        <div key={`in-${input.name}-${i}`} className="flex flex-col items-center relative">
                                            <CircleBadge
                                                as="button"
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200`}
                                                title={val}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setPickerRole((prev) => (prev === input.name ? null : input.name));
                                                }}
                                                onDragOver={(e) => {
                                                    // Allow drop only when dragged resource has matching typeId
                                                    if (dragSource && dragSource.typeId && input.typeId && dragSource.typeId === input.typeId) {
                                                        e.preventDefault();
                                                    }
                                                }}
                                                onDrop={(e) => { e.preventDefault(); handleDropOnInput(input.id, input.name, e); setPickerRole(null); }}
                                                onContextMenu={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    if (isBound && inputBindingId) {
                                                        onClearInputBinding(inputBindingId as ResourceIdJson);
                                                    }
                                                }}
                                            >
                                                <span className="text-xs font-semibold">IN</span>
                                            </CircleBadge>
                                            <div className="text-[11px] text-gray-700 mt-1">{input.name}</div>

                                            {/* If this is a WhileStep or ForStep, render an iterate toggle for one input */}

                                            {(stepKind === CONSTANTS.STEP.while || stepKind === CONSTANTS.STEP.for) && (
                                                <div className="mt-2 text-[11px]">
                                                    <label className="inline-flex items-center gap-2">
                                                        <input
                                                            type="radio"
                                                            name="iteratingRole"
                                                            checked={iteratingRoleId === input.id}
                                                            onChange={() => setIteratingRoleId(input.id)}
                                                        />
                                                        <span>Iterating input</span>
                                                    </label>
                                                </div>
                                            )}

                                            {pickerRole === input.name && (
                                                <div className="absolute z-10 top-12 left-1/2 -translate-x-1/2 w-64 max-h-56 overflow-auto bg-white border border-gray-200 rounded shadow-lg" onClick={(e) => e.stopPropagation()}>
                                                    <div className="sticky top-0 bg-gray-50 text-[11px] text-gray-600 px-2 py-1">Choose a file</div>
                                                    <ul className="py-1">
                                                        {resources.map((resource, i) => (
                                                            <li key={i}>
                                                                <button
                                                                    className="w-full text-left px-3 py-2 text-sm hover:bg-green-50"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        onBindInputPath(input.id, resource);
                                                                        setPickerRole(null);
                                                                    }}
                                                                >
                                                                    {resource.exposedData.semanticIdentity}
                                                                </button>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                    <div className="border-t text-[11px] text-gray-500 px-2 py-1">Right-click input to clear</div>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })
                            )}
                        </div>
                    </div>

                    {/* Current step outputs */}
                    <div>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Outputs</div>
                        <div className="flex flex-wrap gap-3">
                            {outputRoles.length === 0 ? (
                                <div className="text-sm text-gray-500">No outputs</div>
                            ) : (
                                outputRoles.map((output, idx) => {
                                    const outputBindingId = bindingOwner.resourceBindings?.outputBindings?.[output.id] as string | undefined;
                                    const isBound = !!outputBindingId && outputBindingId in (resourceMap || {});
                                    const circleCls = isBound
                                        ? 'bg-red-500 text-white border-red-600'
                                        : 'bg-white text-red-600 border-red-500 hover:bg-red-50';
                                    let val: string = '<UNBOUND>';
                                    if (isBound && outputBindingId) {
                                        const outEntry = (resourceMap[outputBindingId] as ResourceMetaActiveJson | undefined) ?? undefined;
                                        const semId = (outEntry?.exposedData as any)?.semanticIdentity as string | undefined;
                                        val = semId ?? '<UNBOUND>'; // ATTENTION
                                    }

                                    return (
                                        <div key={`out-${output.name}-${idx}`} className="flex flex-col items-center">
                                            <CircleBadge
                                                as={(stepKind !== CONSTANTS.STEP.work) ? 'button' : 'div'}
                                                draggable={(stepKind !== CONSTANTS.STEP.work)}
                                                onDragStart={(e) => {
                                                    if (!outputBindingId) return;
                                                    // Build a drag payload similar to ResourceMapsPanel
                                                    const dragPayload = {
                                                        roleId: output.id,
                                                        roleName: output.name,
                                                        typeId: output.typeId,
                                                        bindingKey: outputBindingId,
                                                        sourceStepId: outputBindingId.split('__').slice(-1)[0]
                                                    } as import('./ResourceMapsPanel').DragSource;
                                                    // set local dragSource state so inputs will see it immediately
                                                    setDragSource(dragPayload);
                                                    // Also emit the custom event for backward compatibility
                                                    const dragEvent = new CustomEvent('toolproof-drag-source', { detail: dragPayload });
                                                    document.dispatchEvent(dragEvent);
                                                }}
                                                onDragEnd={() => {
                                                    const endEvent = new CustomEvent('toolproof-drag-end');
                                                    document.dispatchEvent(endEvent);
                                                }}
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200`}
                                                title={val}
                                            >
                                                <span className="text-xs font-semibold">OUT</span>
                                            </CircleBadge>
                                            <div className="text-[11px] text-gray-700 mt-1">{output.name}</div>
                                        </div>
                                    )
                                })
                            )}
                        </div>
                    </div>

                    <div className="text-xs text-gray-500">
                        Tip: Drag a red output onto a green input to bind it; or click an input to choose from preset calculator json files. Right-click an input to clear.
                    </div>
                </div>
            </div>
        </div>
    );
}
