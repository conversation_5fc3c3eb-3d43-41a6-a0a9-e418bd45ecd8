
type CircleBadgeProps = {
  as?: 'div' | 'button';
  className?: string;
  title?: string;
  draggable?: boolean;
  children?: React.ReactNode;
  onClick?: React.MouseEventHandler;
  onContextMenu?: React.MouseEventHandler;
  onDragStart?: React.DragEventHandler;
  onDragEnd?: React.DragEventHandler;
  onDrop?: React.DragEventHandler;
  onDragOver?: React.DragEventHandler;
  type?: 'button' | 'submit' | 'reset';
};

export default function CircleBadge({
  as = 'div',
  className = '',
  title,
  draggable,
  children,
  onClick,
  onContextMenu,
  onDragStart,
  onDragEnd,
  onDrop,
  onDragOver,
  type = 'button',
}: CircleBadgeProps) {
  const baseCls = `w-10 h-10 rounded-full flex items-center justify-center ${className}`.trim();

  // commonProps typed to accommodate both div and button attributes
  const commonProps: React.HTMLAttributes<HTMLElement> & Partial<React.ButtonHTMLAttributes<HTMLButtonElement>> = {
    className: baseCls,
    title,
    draggable,
    onDragStart,
    onDragEnd,
    onDrop,
    onDragOver,
    onContextMenu,
  };

  if (as === 'button') {
    return (
      <button {...commonProps} onClick={onClick} type={type}>
        {children}
      </button>
    );
  }

  return <div {...commonProps}>{children}</div>;
}
