import React from 'react';
import { Conditional<PERSON>rapper<PERSON><PERSON>, Implementation<PERSON><PERSON><PERSON><PERSON>, WorkStepJson } from '@toolproof/_schemas';
import WorkStepTile from './WorkStepTile';

interface ConditionalWrapperTileProps {
	wrapper: ConditionalWrapperJson;
	implementation: ImplementationMeta<PERSON>son;
	workStep: WorkStepJson;
	isSelected: boolean;
	onClick: () => void;
	label?: string; // optional label like 'While' / 'For' / 'Case'
}

export default function ConditionalWrapperTile({ wrapper, implementation, workStep, isSelected, onClick, label }: ConditionalWrapperTileProps) {
	return (
		<div className="w-full flex flex-col gap-1">
			<WorkStepTile implementation={implementation} workStep={workStep} isSelected={isSelected} onClick={onClick} />
		</div>
	);
}
