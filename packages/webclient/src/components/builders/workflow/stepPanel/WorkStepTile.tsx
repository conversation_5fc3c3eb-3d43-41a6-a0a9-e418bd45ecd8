import { ImplementationMetaJson, WorkStepJson } from '@toolproof/_schemas';
import React from 'react';

interface WorkStepTileProps {
	implementation: ImplementationMetaJson;
	workStep: WorkStepJson;
	isSelected: boolean;
	onClick: () => void;
}

const WorkStepTile: React.FC<WorkStepTileProps> = ({ implementation, workStep, isSelected, onClick }) => {
	return (
		<button
			className={`w-full text-left rounded-md px-3 py-2 border ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'} hover:border-blue-400`}
			onClick={onClick}
			title={implementation.name}
		>
			<div className="text-sm font-medium text-gray-800 truncate">{implementation.name || 'Implementation'}</div>
			<div className="text-[11px] text-gray-500 truncate">{workStep.id}</div>
		</button>
	);
};

export default WorkStepTile;
