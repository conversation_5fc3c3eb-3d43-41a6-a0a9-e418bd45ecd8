import React, { useMemo, useState } from 'react';
import { ConditionJson } from '@/types';
import { JsonEditor } from '@/components/builders/_lib/JsonEditor';

interface ConditionTileProps {
	condition: ConditionJson;
	onEdit: (next: ConditionJson) => void;
	label?: string; // optional kind label like 'While' or 'For'
}

export default function ConditionTile({ condition, onEdit, label }: ConditionTileProps) {
	const [open, setOpen] = useState(false);
	const [text, setText] = useState<string>(() => JSON.stringify(condition, null, 2));
	const [error, setError] = useState<string | null>(null);

	const preview = useMemo(() => {
		try {
			return JSON.stringify(condition);
		} catch {
			return '';
		}
	}, [condition]);

	const handleConfirm = () => {
		try {
			const parsed = JSON.parse(text) as ConditionJson;
			onEdit(parsed);
			setError(null);
			setOpen(false);
		} catch (e) {
			const msg = e instanceof Error ? e.message : String(e);
			setError(msg);
		}
	};

	return (
		<div className="w-full">
			<button
				type="button"
				className="w-full text-left rounded-md px-3 py-2 border border-gray-200 bg-white hover:border-blue-400"
				title={preview}
				onClick={() => { setText(JSON.stringify(condition, null, 2)); setError(null); setOpen(true); }}
			>
				<div className="text-xs font-medium text-gray-600 truncate">{label ?? 'Condition'}</div>
				<div className="text-[11px] text-gray-500 truncate">Hover to preview, click to edit</div>
			</button>

			{open && (
				<div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
					<div className="bg-white rounded shadow-lg max-w-2xl w-full p-6 m-4">
						<h3 className="text-lg font-medium mb-3">Edit Condition (JSON)</h3>
						<JsonEditor
							legend="Condition"
							valueText={text}
							onChangeText={(v) => { setText(v); setError(null); }}
							parseError={error}
							heightClass="h-48"
						/>
						<div className="mt-4 flex justify-end gap-2">
							<button className="px-3 py-2 rounded border" onClick={() => setOpen(false)}>Cancel</button>
							<button className="px-3 py-2 rounded bg-blue-600 text-white" onClick={handleConfirm}>OK</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
