import { ConditionalWrapper<PERSON><PERSON>, Implementation<PERSON><PERSON><PERSON><PERSON>, WorkStepJson } from '@toolproof/_schemas';
import React from 'react';
import ConditionalWrapperTile from './ConditionalWrapperTile';

type BranchItem = {
  wrapper: ConditionalWrapperJson;
  implementation: ImplementationMetaJson;
  workStep: WorkStepJson;
};

interface BranchStepTileProps {
  items: BranchItem[];
  isSelected: boolean;
  selectedCaseIndex?: number | null;
  onClick: () => void;
  onClickCase?: (caseIndex: number) => void;
  onAddBranchStepCase?: (implementationId: string) => void;
}

const BranchStepTile: React.FC<BranchStepTileProps> = ({ items, isSelected, selectedCaseIndex = null, onClick, onClickCase, onAddBranchStepCase }) => {
  const cur = Math.max(0, Math.min(selectedCaseIndex ?? 0, Math.max(items.length - 1, 0)));
  const canLeft = items.length > 0 && cur > 0;
  const canRight = items.length > 0 && cur < items.length - 1;
  return (
    <div
      className={`w-full rounded-md border ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'} hover:border-blue-400`}
      onClick={onClick}
      onDragOver={(e) => e.preventDefault()}
      onDrop={(e) => {
        e.preventDefault();
        const implementationId = e.dataTransfer?.getData('application/toolproof-implementation-id');
        if (!implementationId) return;
        onAddBranchStepCase && onAddBranchStepCase(implementationId);
      }}
    >
      <div className="text-xs font-medium text-gray-600 px-3 pt-2">Branch</div>
      <div className="flex items-center gap-2 p-3 pt-2">
        <button
          type="button"
          className={`shrink-0 h-7 w-7 flex items-center justify-center rounded border ${canLeft ? 'border-gray-300 text-gray-700 hover:bg-blue-100' : 'border-gray-200 text-gray-400 opacity-50 cursor-not-allowed'}`}
          title="Previous case"
          onClick={(e) => {
            e.stopPropagation();
            if (!canLeft || !onClickCase) return;
            onClickCase(cur - 1);
          }}
          disabled={!canLeft}
          aria-disabled={!canLeft}
        >
          {/* Left arrow */}
          <span aria-hidden>←</span>
        </button>
        <div className="flex-1">
          {items.length > 0 ? (
            <div className="min-w-[160px]">
              <ConditionalWrapperTile
                wrapper={items[cur].wrapper}
                implementation={items[cur].implementation}
                workStep={items[cur].workStep}
                isSelected={isSelected}
                onClick={() => onClickCase && onClickCase(cur)}
                label={`Case ${cur + 1}`}
              />
            </div>
          ) : (
            <div className="text-sm text-gray-500 px-3 py-2">No cases</div>
          )}
        </div>
        <button
          type="button"
          className={`shrink-0 h-7 w-7 flex items-center justify-center rounded border ${canRight ? 'border-gray-300 text-gray-700 hover:bg-blue-100' : 'border-gray-200 text-gray-400 opacity-50 cursor-not-allowed'}`}
          title="Next case"
          onClick={(e) => {
            e.stopPropagation();
            if (!canRight || !onClickCase) return;
            onClickCase(cur + 1);
          }}
          disabled={!canRight}
          aria-disabled={!canRight}
        >
          {/* Right arrow */}
          <span aria-hidden>→</span>
        </button>
      </div>
    </div>
  );
};

export default BranchStepTile;
