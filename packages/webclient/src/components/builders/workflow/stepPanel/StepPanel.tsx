import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, While<PERSON><PERSON><PERSON><PERSON>, ForStep<PERSON>son } from '@toolproof/_schemas';
import type { SelectedIndex } from '@/types'
import { CONSTANTS } from '@toolproof/_lib/constants';
import WorkStepTile from './WorkStepTile';
import WhileStepTile from './WhileStepTile';
import ForStepTile from './ForStepTile';
import BranchStepTile from './BranchStepTile';

interface StepPanelProps {
    steps: (StepJson)[];
    selectedIndex: SelectedIndex | null;
    implementationMap: Map<string, ImplementationMetaJson>; // ATTENTION: Can't we just pass activeImplementation as a prop?
    workStepMap: Map<string, WorkStepJson>; // ATTENTION: Can't we just pass bindingOwner as a prop? Maybe not, because we need to find workSteps by id for all steps, not just the active one.
    onSelect: (index: SelectedIndex) => void;
    onAddBranchStepCase: (implementationId: string) => void;
}

export default function StepPanel({ steps, selectedIndex, implementationMap, workStepMap, onSelect, onAddBranchStepCase }: StepPanelProps) {

    return (
        <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Steps</h2>
                <p className="text-xs text-gray-500">Click to focus a step.</p>
            </div>
            <div className="flex-1 overflow-y-auto p-2">
                {steps.length === 0 ? (
                    <div className="text-sm text-gray-500 px-2 py-3">No steps yet. Drag a implementation to add it.</div>
                ) : (
                    <ul className="space-y-1">
                        {steps.map((step, i) => {
                            const isSelectedStep = selectedIndex?.stepIndex === i;
                            switch (step.kind) {
                                case CONSTANTS.STEP.work: {
                                    const workStep = step as WorkStepJson;
                                    const implementation = implementationMap.get(workStep.implementationId);
                                    if (!implementation) return null;
                                    return (
                                        <li key={step.id}>
                                            <WorkStepTile
                                                implementation={implementation}
                                                workStep={workStep}
                                                isSelected={isSelectedStep}
                                                onClick={() => onSelect({ stepIndex: i, caseIndex: null })}
                                            />
                                        </li>
                                    );
                                }
                                case CONSTANTS.STEP.branch: {
                                    const branchStep = step as BranchStepJson;
                                    const items = branchStep.cases
                                        .map((w) => {
                                            const innerId = w.what.id as StepIdJson;
                                            const workStep = workStepMap.get(innerId);
                                            if (!workStep) return null;
                                            const implementation = implementationMap.get(workStep.implementationId);
                                            if (!implementation) return null;
                                            return { wrapper: w, implementation, workStep } as { wrapper: typeof w; implementation: ImplementationMetaJson; workStep: WorkStepJson };
                                        })
                                        .filter((x): x is { wrapper: typeof branchStep.cases[number]; implementation: ImplementationMetaJson; workStep: WorkStepJson } => Boolean(x));
                                    return (
                                        <li key={step.id}>
                                            <BranchStepTile
                                                items={items}
                                                isSelected={isSelectedStep}
                                                selectedCaseIndex={isSelectedStep ? (selectedIndex?.caseIndex ?? null) : null}
                                                onClick={() => onSelect({ stepIndex: i, caseIndex: null })}
                                                onClickCase={(caseIndex: number) => onSelect({ stepIndex: i, caseIndex })}
                                                onAddBranchStepCase={onAddBranchStepCase}
                                            />
                                        </li>
                                    );
                                }
                                case CONSTANTS.STEP.while: {
                                    const whileStep = step as WhileStepJson;
                                    const innerId = whileStep.case.what.id as StepIdJson;
                                    const workStep = workStepMap.get(innerId);
                                    if (!workStep) return null;
                                    const implementation = implementationMap.get(workStep.implementationId);
                                    if (!implementation) return null;
                                    return (
                                        <li key={step.id}>
                                            <WhileStepTile
                                                wrapper={whileStep.case}
                                                implementation={implementation} // ATTENTION: can't implementation and workStep be inferred from wrapper?
                                                workStep={workStep}
                                                isSelected={isSelectedStep}
                                                onClick={() => onSelect({ stepIndex: i, caseIndex: null })}
                                            />
                                        </li>
                                    );
                                }
                                case CONSTANTS.STEP.for: {
                                    const forStep = step as ForStepJson;
                                    const innerId = forStep.case.what.id as StepIdJson;
                                    const workStep = workStepMap.get(innerId);
                                    if (!workStep) return null;
                                    const implementation = implementationMap.get(workStep.implementationId);
                                    if (!implementation) return null;
                                    return (
                                        <li key={step.id}>
                                            <ForStepTile
                                                wrapper={forStep.case}
                                                implementation={implementation}
                                                workStep={workStep}
                                                isSelected={isSelectedStep}
                                                onClick={() => onSelect({ stepIndex: i, caseIndex: null })}
                                            />
                                        </li>
                                    );
                                }
                                default:
                                    return null;
                            }
                        })}
                    </ul>
                )}
            </div>
        </div>
    );
}
