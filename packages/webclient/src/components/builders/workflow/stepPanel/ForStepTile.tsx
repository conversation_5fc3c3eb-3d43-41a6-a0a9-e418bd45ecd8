import { Imple<PERSON><PERSON><PERSON><PERSON><PERSON>, WorkStep<PERSON>son, ConditionalWrapper<PERSON><PERSON> } from '@toolproof/_schemas';
import React from 'react';
import ConditionalWrapperTile from './ConditionalWrapperTile';

interface ForStepTileProps {
	wrapper: ConditionalWrapper<PERSON>son;
	implementation: ImplementationMeta<PERSON>son;
	workStep: WorkStepJson;
	isSelected: boolean;
	onClick: () => void;
}

const ForStepTile: React.FC<ForStepTileProps> = ({ wrapper, implementation, workStep, isSelected, onClick }) => {
	return (
		<ConditionalWrapperTile
			wrapper={wrapper}
			implementation={implementation}
			workStep={workStep}
			isSelected={isSelected}
			onClick={onClick}
			label="For"
		/>
	);
};

export default ForStepTile;
