'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, TypeMeta<PERSON>son, SignatureMeta<PERSON>son, ImplementationMeta<PERSON>son, ExecutionJson, StepJson, WorkStepJson, ConditionalWrapperJson, BranchStepJson, WhileStepJson, ForStepJson, ResourceMapJson, WorkflowSpecJson, ResourceIdJson, ResourceMetaActiveJson } from '@toolproof/_schemas';
import type { SelectedIndex, ExposedDataHack, ResourceMerged } from '@/types';
import { StepConst } from '@toolproof/_lib/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import WorkflowCanvas from '@/components/builders/workflow/WorkflowCanvas';
// import ImplementationLibrary from '@/components/builders/workflow-builder/ImplementationLibrary'; // Disabled for now
import WorkflowToolbar from '@/components/builders/workflow/WorkflowToolbar';
import ResourceBindingsPanel from '@/components/builders/workflow/ResourceBindingsPanel';
import StepPanel from '@/components/builders/workflow/stepPanel/StepPanel';
import Modal from '@/components/builders/workflow/Modal';
import { getUnboundInputs, getUnboundOutputs, bindOutputs } from '@/components/builders/workflow/_lib/utils';
import { runRemoteGraph } from '@/_lib/server/invokeRemoteGraph';
import { useMeta, usePrimitivesData, useResourcesMerged } from '@/_lib/client/firebaseWebHelpers';
import { getNewId } from '@/_lib/server/firebaseAdminHelpers';
// import { validateWorkStep, validateResourceMap } from '@toolproof/_validator';
import { writeToCAFS } from '@/_lib/server/firebaseAdminHelpers';
import { useState, useCallback, useRef, useEffect, useMemo } from 'react';


export default function WorkflowBuilder() {
    const containerRef = useRef<HTMLDivElement>(null);
    const [showXr, setShowXr] = useState(false);

    const [workflowSpec, setWorkflowSpec] = useState<WorkflowSpecJson | null>(null);
    const [workflowSpecId, setWorkflowSpecId] = useState<string | null>(null);
    const [workflowId, setWorkflowId] = useState<string | null>(null);
    const [workflowName, setWorkflowName] = useState('Untitled Workflow'); // ATTENTION: workflow is not a Concept

    const { items: typesData, loading: typesLoading, error: typesError } = usePrimitivesData<TypeDataJson>(CONSTANTS.STORAGE.tp_primitives, CONSTANTS.PRIMITIVES.types);

    const { items: implementationsMeta, loading: implementationsLoading, error: implementationsError } = useMeta<ImplementationMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.implementations, false);

    const { items: resourcesMerged, loading: loadingResourcesMerged, error: errorsResourcesMerged } = useResourcesMerged<ResourceMerged, ExposedDataHack>(CONSTANTS.NON_PRIMITIVES.resources, 'TYPE-wSo0cBZk3yK9F5DUb9zV', CONSTANTS.STORAGE.tp_resources);

    // console.log('typesData:', JSON.stringify(typesData, null, 2));
    // console.log('implementationsMeta:', JSON.stringify(implementationsMeta, null, 2));
    // console.log('resourcesMerged:', JSON.stringify(resourcesMerged, null, 2));

    const [selectedResource, setSelectedResource] = useState<ResourceMetaActiveJson | null>(null);

    const implementationMap = useMemo(() => {
        return implementationsMeta.reduce((map, implementation) => {
            map.set(implementation.id, implementation);
            return map;
        }, new Map<string, ImplementationMetaJson>());
    }, [implementationsMeta]);

    const [workStepMap, setWorkStepMap] = useState<Map<string, WorkStepJson>>(new Map()); // ATTENTION: is workStepMap still needed?

    // DOC: Condition-modal state
    const [stepKind, setStepKind] = useState<StepConst | 'branch-case' | null>(null); // ATTENTION
    const [modalIsOpen, setModalIsOpen] = useState(false);
    const [jsonText, setJsonText] = useState<string>('{}');
    const [jsonParseError, setJsonParseError] = useState<string | null>(null);
    const [pendingImplementation, setPendingImplementation] = useState<ImplementationMetaJson | null>(null);

    // DOC: Track selected step (and optional branch case) for StepPanel/ResourceBindingsPanel
    const [selectedIndex, setSelectedIndex] = useState<SelectedIndex | null>(null);

    const activeStep =
        selectedIndex != null && workflowSpec?.workflow?.steps?.[selectedIndex.stepIndex] !== undefined
            ? workflowSpec.workflow.steps[selectedIndex.stepIndex]
            : null;

    // DOC: When activeStep is a wrapper (i.e. branch, while, or for), resolve the inner WorkStep which owns bindings
    let bindingOwner: ExecutionJson | null;
    if (!activeStep) {
        bindingOwner = null;
    } else {
        switch (activeStep.kind) {
            case CONSTANTS.STEP.work: {
                bindingOwner = activeStep.execution as ExecutionJson;
                break;
            }
            case CONSTANTS.STEP.branch: {
                const cases = (activeStep as BranchStepJson).cases ?? [];
                const rawIdx = selectedIndex?.caseIndex ?? 0;
                const caseIdx = Math.max(0, Math.min(rawIdx, Math.max(cases.length - 1, 0)));
                const inner = cases?.[caseIdx]?.what.execution as ExecutionJson | undefined;
                bindingOwner = inner ?? null;
                break;
            }
            case CONSTANTS.STEP.while:
            case CONSTANTS.STEP.for: {
                const inner = (activeStep as WhileStepJson).case?.what.execution as ExecutionJson | undefined;
                bindingOwner = inner ?? null;
                break;
            }
            default: {
                throw new Error('Unknown step kind');
            }
        }
    }

    // DOC: Generate workflowSpecId and workflowId if not present
    useEffect(() => {

        const asyncWrapper = async () => {
            // Ensure we have a workflow-spec id
            if (!workflowSpecId) {
                const id = await getNewId('WORKFLOWSPEC'); // ATTENTION
                setWorkflowSpecId(id);
            }

            // Ensure we have a workflow id as well
            if (!workflowId) {
                const id = await getNewId('WORKFLOW'); // ATTENTION
                setWorkflowId(id);
            }
        };

        asyncWrapper();

    }, [workflowSpecId, workflowId]);

    // DOC: When both ids are available, initialize workflowSpec
    useEffect(() => {
        let mounted = true;
        if (workflowSpecId && workflowId) {
            // Only set if not already set or if ids don't match
            setWorkflowSpec((prev) => {
                if (!mounted) return prev;
                if (!prev || prev.id !== workflowSpecId || prev.workflow?.id !== workflowId) {
                    return {
                        id: workflowSpecId,
                        workflow: {
                            id: workflowId,
                            steps: []
                        },
                        resourceMaps: [{}]
                    } as WorkflowSpecJson;
                }
                return prev;
            });
        }
        return () => {
            mounted = false;
        };
    }, [workflowSpecId, workflowId, setWorkflowSpec]);

    // DOC: When all inputs are bound for the active step, auto-bind outputs
    useEffect(() => {
        if (!workflowSpec) return;
        if (!bindingOwner) return;
        const unboundInputs = getUnboundInputs(bindingOwner, workflowSpec.resourceMaps?.[0]);
        const isFullyInputBound = unboundInputs.length === 0;
        // console.log(`Active step ${bindingOwner.id} fully bound:`, isFullyInputBound, 'unboundKeys:', unboundInputs);
        if (isFullyInputBound) {
            const unboundOutputs = getUnboundOutputs(bindingOwner, workflowSpec.resourceMaps?.[0]);
            const isFullyOutputBound = unboundOutputs.length === 0;
            // console.log(`Active step ${bindingOwner.id} fully bound:`, isFullyOutputBound, 'unboundKeys:', unboundOutputs);
            if (isFullyOutputBound) return; // Nothing to do if outputs are already fully bound
            const boundOutputs = bindOutputs(bindingOwner);
            // console.log(`Active step ${bindingOwner.id} bound outputs:`, boundOutputs);
            setWorkflowSpec((prev) => {
                if (!prev) return prev;
                const newResourceMaps = [...prev.resourceMaps];
                newResourceMaps[0] = {
                    ...newResourceMaps[0],
                    ...boundOutputs
                };
                return {
                    ...prev,
                    resourceMaps: newResourceMaps
                };
            });
        }
    }, [workflowSpec, bindingOwner]);

    // DOC: Helper function to construct a WorkStep object from an implementationMetaJson (does not append to workflow.steps)
    const makeWorkStepFromImplementation = useCallback(async (implementation: ImplementationMetaJson): Promise<WorkStepJson> => {
        if (!workflowSpec) throw new Error('workflowSpec not initialized');
        if (bindingOwner) {
            const unbound = getUnboundInputs(bindingOwner, workflowSpec.resourceMaps?.[0]);
            if (unbound.length > 0) {
                alert(`Provide inputs for the current step before adding another. Missing: ${unbound.join(', ')}`);
                throw new Error('unbound inputs');
            }
        }

        const workStepId = await getNewId('WORKSTEP');
        const executionId = workStepId.replace('WORKSTEP', 'EXECUTION'); // ATTENTION

        const inputBindings: Record<string, string> = {};
        const inputs = implementation.roles?.inputs ?? {};
        Object.keys(inputs).forEach((roleId) => {
            const resourceId = roleId + '__' + workStepId;
            inputBindings[roleId] = resourceId;
        });

        const outputBindings: Record<string, string> = {};
        const outputs = implementation.roles?.outputs ?? {};
        Object.keys(outputs).forEach((roleId) => {
            const resourceId = roleId + '__' + workStepId;
            outputBindings[roleId] = resourceId;
        });

        const newWorkStep: WorkStepJson = {
            id: workStepId,
            kind: CONSTANTS.STEP.work,
            execution: {
                id: executionId,
                implementationId: implementation.id,
                resourceBindings: {
                    inputBindings,
                    outputBindings
                }
            }
        };
        return newWorkStep;
    }, [bindingOwner, workflowSpec]);

    // DOC: Handler for dropping an implementation onto the 'WorkStep' area of the WorkflowCanvas
    const onDropWorkStep = useCallback(async (implementation: ImplementationMetaJson) => {
        try {
            const newWorkStep = await makeWorkStepFromImplementation(implementation);
            setWorkflowSpec((prev) => {
                const base = (prev ?? {
                    id: workflowSpecId ?? '',
                    workflow: { id: workflowId ?? '', steps: [] },
                    resourceMaps: []
                }) as WorkflowSpecJson;
                return {
                    ...base,
                    workflow: { ...base.workflow, steps: [...base.workflow.steps, newWorkStep] }
                };
            });
            setWorkStepMap(prev => new Map(prev).set(newWorkStep.id ?? '', newWorkStep));
            setSelectedIndex((prev) => (prev == null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));
        } catch (err) {
            // makeWorkStepFromImplementation already alerts for input binding issues; ignore errors
            console.warn('Failed to create workStep:', err);
        }
    }, [makeWorkStepFromImplementation, workflowSpecId, workflowId]);

    // DOC: Prepare and open the condition modal for BranchStep, WhileStep, or ForStep
    const prepareModal = useCallback((kind: StepConst | 'branch-case', implementation: ImplementationMetaJson) => {
        setStepKind(kind);
        setPendingImplementation(implementation);
        setJsonText('{}');
        setJsonParseError(null);
        setModalIsOpen(true);
    }, []);

    // DOC: Handler for dropping an implementation onto the 'BranchStep' area of the WorkflowCanvas
    const onDropBranchStep = useCallback(async (implementation: ImplementationMetaJson) => {
        console.log('onDropBranchStep is not yet implemented');
        return;
        prepareModal(CONSTANTS.STEP.branch, implementation);
    }, [prepareModal]);

    // DOC: Handler for dropping an implementation onto the 'Add Case' area of a BranchStepTile
    const onAddBranchStepCase = useCallback((implementationId: string) => {
        const implementation = implementationMap.get(implementationId);
        if (!implementation) {
            console.warn('Dropped implementation id not found for BranchStepTile:', implementationId);
            return;
        }
        // Only allow when active step is a branch
        if (!activeStep || activeStep.kind !== 'branch') return;
        prepareModal('branch-case', implementation);
    }, [implementationMap, activeStep, prepareModal]);

    // DOC: Handler for dropping an implementation onto the 'WhileStep' area of the WorkflowCanvas
    const onDropWhileStep = useCallback(async (implementation: ImplementationMetaJson) => {
        console.log('onDropWhileStep is not yet implemented');
        return;
        prepareModal(CONSTANTS.STEP.while, implementation);
    }, [prepareModal]);

    // DOC: Handler for dropping an implementation onto the 'ForStep' area of the WorkflowCanvas
    const onDropForStep = useCallback(async (implementation: ImplementationMetaJson) => {
        prepareModal(CONSTANTS.STEP.for, implementation);
    }, [prepareModal]);

    // DOC: Confirm and close the condition modal, creating the appropriate wrapper step
    const confirmModal = useCallback(async () => {
        if (!pendingImplementation) return;
        if (stepKind !== CONSTANTS.STEP.for) return; // ATTENTION: only ForStep is implemented for now
        if (!selectedResource) return; // ATTENTION
        try {
            // const parsed = JSON.parse(jsonText) as ConditionJson;
            // Create inner WorkStep
            const workStep = await makeWorkStepFromImplementation(pendingImplementation);

            let step: WhileStepJson | ForStepJson | BranchStepJson | null = null;

            const resMap: ResourceMapJson = {
                ...(workflowSpec?.resourceMaps?.[0])
            };

            switch (stepKind) {
                case CONSTANTS.STEP.branch: {
                    const stepId = await getNewId(CONSTANTS.STEP.branch);
                    step = {
                        id: stepId,
                        kind: CONSTANTS.STEP.branch,
                        cases: [wrapper],
                    } as BranchStepJson;
                    break;
                }
                case 'branch-case': {
                    // Append a case to the currently selected BranchStep
                    if (!selectedIndex) throw new Error('No selected step for adding a branch case');
                    const idx = selectedIndex.stepIndex;
                    setWorkflowSpec((prev) => {
                        if (!prev) return prev;
                        const steps = [...(prev.workflow?.steps || [])];
                        const target = steps[idx];
                        if (!target || target.kind !== CONSTANTS.STEP.branch) return prev;
                        const s = target as BranchStepJson;
                        const cases = [...s.cases, wrapper];
                        const updated: BranchStepJson = { ...s, cases: cases as [ConditionalWrapperJson, ...ConditionalWrapperJson[]] };
                        steps[idx] = updated;
                        return { ...prev, workflow: { ...prev.workflow, steps } };
                    });
                    // Keep selection on the same BranchStep but set caseIndex to the newly added case
                    setSelectedIndex((prev) => {
                        if (!prev) return null;
                        const currentCasesLen = (activeStep && activeStep.kind === CONSTANTS.STEP.branch && (activeStep as BranchStepJson).cases ? (activeStep as BranchStepJson).cases.length : 0);
                        const baseIndex = (prev.caseIndex ?? currentCasesLen);
                        return { stepIndex: prev.stepIndex, caseIndex: baseIndex + 1 };
                    });
                    // Add the inner workStep to maps as well
                    if (workStep.id) {
                        setWorkStepMap(prev => new Map(prev).set(workStep.id ?? '', workStep));
                    }
                    // Close modal & reset
                    setStepKind(null);
                    setModalIsOpen(false);
                    setPendingImplementation(null);
                    setJsonText('{}');
                    setJsonParseError(null);
                    return; // early return: we've updated state for branch-case
                }
                case CONSTANTS.STEP.while: {
                    const stepId = await getNewId(CONSTANTS.STEP.while);
                    step = {
                        id: stepId,
                        kind: CONSTANTS.STEP.while,
                        case: wrapper
                    } as WhileStepJson;
                    break;
                }
                case CONSTANTS.STEP.for: {
                    // Create inner PredicateStep
                    const lessThan = implementationMap.get(CONSTANTS.JOB_LESS_THAN);
                    if (!lessThan) throw new Error(`${CONSTANTS.JOB_LESS_THAN} job not found`);
                    const predicateStep = await makeWorkStepFromImplementation(lessThan);

                    // Create binding for the PredicateStep
                    resMap[`${CONSTANTS.ROLE_LESS_THAN_TARGET}__${predicateStep.id}`] = { timestamp: '', path: selectedResource.path, exposedData: selectedResource.exposedData };

                    // Create ConditionalWrapper
                    const wrapper: ConditionalWrapperJson = {
                        when: predicateStep,
                        what: workStep
                    };

                    const stepId = await getNewId(CONSTANTS.STEP.for);
                    step = {
                        id: stepId,
                        kind: CONSTANTS.STEP.for,
                        case: wrapper
                    } as ForStepJson;
                    break;
                }
                default: {
                    break;
                }
            }

            // Append to workflow
            setWorkflowSpec((prev) => {
                const base = (prev ?? {
                    id: workflowSpecId ?? '',
                    workflow: { id: workflowId ?? '', steps: [] },
                    resourceMaps: []
                }) as WorkflowSpecJson;
                if (!step) return base;
                return {
                    ...base,
                    workflow: { ...base.workflow, steps: [...base.workflow.steps, step] },
                    resourceMaps: [resMap] // ATTENTION: overwrites parallel maps
                };
            });

            // Add the inner workStep to maps as well
            if (workStep.id) {
                setWorkStepMap(prev => new Map(prev).set(workStep.id ?? '', workStep));
            }

            // Advance selection
            setSelectedIndex((prev) => (prev == null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));

            setStepKind(null);
            setModalIsOpen(false);
            setPendingImplementation(null);
            setJsonText('{}');
            setJsonParseError(null);
        } catch (err) {
            const msg = err instanceof Error ? err.message : String(err);
            setJsonParseError(msg);
        }
    }, [stepKind, pendingImplementation, makeWorkStepFromImplementation, workflowSpecId, workflowId, selectedIndex, activeStep, selectedResource, implementationMap, workflowSpec?.resourceMaps]);

    // DOC: Cancel and close the condition modal without changes
    const cancelModal = useCallback(() => {
        setModalIsOpen(false);
        setStepKind(null);
        setPendingImplementation(null);
        setJsonText('{}');
        setJsonParseError(null);
    }, []);

    // DOC: Handler for clicking an implementation in WorkflowCanvas (currently just logs the implementation, can be expanded to show implementation details)
    const handleImplementationClick = useCallback(async (implementation: ImplementationMetaJson) => {
        console.log('Implementation clicked:', implementation.name);
    }, []);

    // DOC: Allow clicking a step in StepPanel to change selection
    const handleSelectStep = useCallback((index: SelectedIndex) => {
        if (!workflowSpec) return;
        setSelectedIndex(index);
    }, [workflowSpec]);

    // DOC: Handler for dispatching workflowSpec to the Engine for execution (currently it's executed directly, but in the future it will be scheduled)
    const handleDispatchWorkflowSpec = useCallback(async () => {
        if (!workflowSpec) return;
        console.log('Dispatching workflowSpec:', JSON.stringify(workflowSpec, null, 2));
        // console.log('activeStep:', JSON.stringify(activeStep, null, 2));
        await runRemoteGraph(workflowSpec);
        (await writeToCAFS(CONSTANTS.SPECIALS.TYPE_WorkflowSpec, `ROLE-ManualCreation__JOBSTEP-${workflowSpec.id.split('-').pop()!}`, JSON.stringify(workflowSpec, null, 2))); // ATTENTION: hack to save WorkflowSpec to Firestore
        return;

    }, [workflowSpec, activeStep]);

    // DOC: Handler for binding an inputRole to a file path
    const onBindInputPath = useCallback((roleId: string, resource: ResourceMetaActiveJson) => {
        if (!bindingOwner) {
            throw new Error('bindingOwner not found');
        }
        setWorkflowSpec((prev) => {
            const base = (prev ?? {
                id: workflowSpecId ?? '',
                workflow: { id: workflowId ?? '', steps: [] },
                resourceMaps: []
            }) as WorkflowSpecJson;
            const resMap = { ...(base.resourceMaps[0] || {}) } as ResourceMapJson;
            const key = bindingOwner.resourceBindings?.inputBindings?.[roleId];
            if (!key) {
                throw new Error(`inputBindings not found for role: ${roleId}`);
            }
            resMap[key] = { timestamp: '', path: resource.path, exposedData: resource.exposedData };

            return {
                ...base,
                resourceMaps: [resMap]
            };
        });
    }, [bindingOwner, workflowId, workflowSpecId]);

    // DOC: Handler for binding an inputRole to a resource pointer (i.e. a resource that already exists in the resourceMap)
    const onBindInputPointer = useCallback((roleId: string, resourcePointer: string) => {
        // console.log('resourcePointer:', resourcePointer);
        if (!bindingOwner) {
            throw new Error('bindingOwner not found');
        }
        setWorkflowSpec((prev) => {
            const base = (prev ?? {
                id: workflowSpecId ?? '',
                workflow: { id: workflowId ?? '', steps: [] },
                resourceMaps: []
            }) as WorkflowSpecJson;
            const resMap = { ...(base.resourceMaps[0] || {}) } as ResourceMapJson;
            const key = bindingOwner.resourceBindings?.inputBindings?.[roleId];
            if (!key) {
                throw new Error(`bindings not found for role: ${roleId}`);
            }
            resMap[key] = { timestamp: '', pointer: resourcePointer, exposedData: {} };

            return {
                ...base,
                resourceMaps: [resMap]
            };
        });
    }, [bindingOwner, workflowId, workflowSpecId]);

    // DOC: Handler for clearing any binding (path or pointer) for all inputs of the active step, as well as any outputs produced by the step
    const onClearInputBinding = useCallback((resourceId: ResourceIdJson) => {
        if (!bindingOwner) {
            throw new Error('bindingOwner not found');
        }
        setWorkflowSpec((prev) => {
            const base = (prev ?? {
                id: workflowSpecId ?? '',
                workflow: { id: workflowId ?? '', steps: [] },
                resourceMaps: []
            }) as WorkflowSpecJson;

            const resMap = { ...(base.resourceMaps[0] || {}) } as ResourceMapJson;
            // ResourceIdJson is a narrowed string key; remove it from the resource map
            if (resourceId in resMap) {
                delete resMap[resourceId];
            }

            // Also remove any outputs produced by the binding owner workStep so downstream
            // resources that depend on this step are cleared as well.
            const outputs = bindingOwner.resourceBindings?.outputBindings || {};
            const outputBindingValues = Object.values(outputs);
            for (const outKey of outputBindingValues) {
                if (outKey in resMap) {
                    delete resMap[outKey];
                }
            }

            return { ...base, resourceMaps: [resMap], workflow: { ...base.workflow } };
        });
    }, [workflowSpecId, workflowId, bindingOwner]);

    if (!workflowSpec) {
        return null;;
    }

    /* const { valid, errors } = validateWorkStep(bindingOwner, implementations);

    console.log('workStepValidation:', { valid, errors }); */

    // const { isValid, errors } = validateResourceMap(workflowSpec.resourceMaps[0], bindingOwner, rolesMeta, typesData);

    // console.log('resourceMap:', JSON.stringify(workflowSpec.resourceMaps[0], null, 2));

    // console.log('resourceMapValidation:', JSON.stringify({ isValid, errors }, null, 2));

    return ( // ATTENTION: consider React.context
        <div className="h-screen flex flex-col bg-gray-50">
            {/* Toolbar */}
            <WorkflowToolbar
                workflowName={workflowName}
                onWorkflowNameChange={setWorkflowName}
                onDispatch={handleDispatchWorkflowSpec}
                onViewXr={() => setShowXr(!showXr)}
                validationResult={null} // ATTENTION: should apply to validation of the whole WorkflowSpec
            />

            <div className="flex-1 flex overflow-hidden">
                {/* WorkflowCanvas */}
                <div className="flex-1 relative">
                    <WorkflowCanvas
                        implementationMap={implementationMap}
                        onImplementationClick={handleImplementationClick}
                    />
                </div>

                {/* StepPanel */}
                <div className="w-80 bg-white border-l border-gray-200">
                    <StepPanel
                        steps={workflowSpec.workflow.steps}
                        workStepMap={workStepMap}
                        implementationMap={implementationMap}
                        selectedIndex={selectedIndex}
                        onSelect={handleSelectStep}
                        onAddBranchStepCase={onAddBranchStepCase}
                    />
                </div>

                {/* ResourceBindingsPanel */}
                {(activeStep && bindingOwner && selectedIndex) ?
                    <div className="w-96 bg-white border-l border-gray-200">
                        <ResourceBindingsPanel
                            activeStep={activeStep as StepJson}
                            selectedIndex={selectedIndex}
                            bindingOwner={bindingOwner}
                            implementationMap={implementationMap}
                            resourceMap={workflowSpec.resourceMaps[0]}
                            resources={resourcesMerged}
                            onBindInputPath={onBindInputPath}
                            onBindInputPointer={onBindInputPointer}
                            onClearInputBinding={onClearInputBinding}
                        />
                    </div>
                    : null} {/* ATTENTION: render a placeholder */}
            </div>

            {/* Quadrants row below canvas */}
            <div className="bg-white border-t border-gray-200">
                <div className="max-w-full mx-auto px-4 py-3">
                    <div className="grid grid-cols-4 gap-4">
                        {['WorkStep', 'BranchStep', 'WhileStep', 'ForStep'].map((name) => {
                            return (
                                <div
                                    key={name}
                                    onDragOver={(e) => e.preventDefault()}
                                    onDrop={async (e) => {
                                        e.preventDefault();
                                        const implementationId = e.dataTransfer?.getData('application/toolproof-implementation-id');
                                        if (!implementationId) return;
                                        const implementation = implementationMap.get(implementationId);
                                        if (!implementation) {
                                            console.warn('Dropped implementation id not found:', implementationId);
                                            return;
                                        }
                                        if (name === 'WorkStep') {
                                            // Reuse existing click logic when dropping onto WorkStep
                                            await onDropWorkStep(implementation);
                                        } else if (name === 'BranchStep') {
                                            await onDropBranchStep(implementation);
                                        } else if (name === 'WhileStep') {
                                            await onDropWhileStep(implementation);
                                        } else if (name === 'ForStep') {
                                            await onDropForStep(implementation);
                                        } else {
                                            console.log(`Canceling step creation for implementation ${implementation.name}`);
                                        }
                                    }}
                                    className="min-h-[64px] flex items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50 p-3"
                                >
                                    <div className="text-sm font-medium text-gray-700">{name}</div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>

            {/* Fullscreen 3D overlay */}
            {showXr && (
                <div
                    className="fixed inset-0 z-50 flex flex-col bg-black bg-opacity-90"
                    style={{ backdropFilter: 'blur(2px)' }}
                >
                    <button
                        className="absolute top-4 right-4 z-60 px-4 py-2 bg-white text-gray-800 rounded shadow hover:bg-gray-200"
                        onClick={() => setShowXr(false)}
                        aria-label="Close 3D scene"
                    >
                        ×
                    </button>
                    <div className="flex-1 flex items-center justify-center">
                        <div
                            ref={containerRef}
                            className="w-full h-full"
                            style={{ maxWidth: '90vw', maxHeight: '90vh' }}
                        />
                    </div>
                </div>
            )}

            {/* JSON modal for predicates */}
            {modalIsOpen && (
                <Modal
                    implementationMap={implementationMap}
                    stepKind={stepKind}
                    resources={resourcesMerged}
                    onSelectResource={setSelectedResource}
                    jsonText={jsonText}
                    setJsonText={setJsonText}
                    jsonParseError={jsonParseError}
                    setJsonParseError={setJsonParseError}
                    onConfirm={confirmModal}
                    onCancel={cancelModal}
                />
            )}
        </div>
    );

}
