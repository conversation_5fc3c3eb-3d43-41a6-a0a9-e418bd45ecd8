import type { <PERSON><PERSON>ap<PERSON><PERSON>, Role<PERSON>son, ExecutionJson } from '@toolproof/_schemas';
import CircleBadge from '@/components/builders/workflow/CircleBadge';


export interface DragSource {
    roleId: string;
    roleName: string;
    typeId?: string;
    bindingKey: string; // the full resourceMap key
    sourceStepId?: string; // optional suffix if present
}

interface ResourceMapsPanelProps {
    bindingOwner: ExecutionJson | null;
    roleMap: Map<string, RoleJson>;
    resourceMap: ResourceMapJson;
    setDragSource: React.Dispatch<React.SetStateAction<DragSource | null>>;
    // Optional: exclude any resources whose step-id suffix matches one of these (e.g., sibling cases of a BranchStep)
    excludeStepIds?: string[];
}

export default function ResourceMapsPanel({ bindingOwner, roleMap, resourceMap, setDragSource, excludeStepIds = [] }: ResourceMapsPanelProps) {
    const cancelDrag = () => setDragSource(null);
    const excludeSet = new Set<string>(excludeStepIds.filter(Boolean));

    return (
        <div>
            {(() => {
                const keys = Object.keys(resourceMap || {});
                if (keys.length === 0) return null;
                // Filter out any resource keys produced by the active workStep.
                const filtered = keys.filter((k) => {
                    if (!k) return false;
                    const parts = k.split('__');
                    const suffix = parts.length > 1 ? parts[parts.length - 1] : undefined;
                    if (!suffix) return true;
                    if (!bindingOwner) return true;
                    // Exclude keys produced by the active step
                    if (suffix.includes(bindingOwner.id.split('-').pop()!)) return false; // ATTENTION: hacky
                    // Exclude keys produced by any explicitly excluded step ids (e.g., other branch cases)
                    if (excludeSet.has(suffix)) return false;
                    return true;
                });
                if (filtered.length === 0) return null;
                return (
                    <div className='bg-blue-100'>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Resources</div>
                        <div className="flex flex-wrap gap-3">
                            {filtered.map((k) => {
                                // Determine role id prefix (before '__') and lookup role
                                const parts = k.split('__');
                                const prefix = parts.length > 0 ? parts[0] : k;
                                const role = roleMap.get(prefix);
                                const direction = role?.direction ?? 'output'; // default to output
                                const isInput = direction === 'input';
                                // Prepare drag payload
                                const suffix = parts.length > 1 ? parts[parts.length - 1] : undefined;
                                const dragPayload: DragSource = {
                                    roleId: prefix,
                                    roleName: role?.name ?? prefix,
                                    typeId: role?.typeId,
                                    bindingKey: k,
                                    sourceStepId: suffix,
                                };

                                return (
                                    <div key={k} className="flex flex-col items-center">
                                        <CircleBadge
                                            as="div"
                                            draggable
                                            onDragStart={(e) => {
                                                // set drag source for drop handlers
                                                setDragSource(dragPayload);
                                                // set minimal data for native drag-and-drop
                                                try { e.dataTransfer?.setData('text/plain', JSON.stringify(dragPayload)); } catch (e) { }
                                            }}
                                            onDragEnd={() => cancelDrag()}
                                            className={isInput ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}
                                            title={`${resourceMap[k].path}`}
                                        >
                                            <span className="text-xs font-semibold">{isInput ? 'IN' : 'OUT'}</span>
                                        </CircleBadge>
                                        <div className="text-[11px] text-gray-700 mt-1 truncate max-w-28" title={k}>{k}</div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                );
            })()}
        </div>
    )

}