import type { ResourceMetaActiveJson, ImplementationMetaJson } from '@toolproof/_schemas';
import { StepConst } from '@toolproof/_lib/types';
import { CONSTANTS } from '@toolproof/_lib/constants'
import { JsonEditor } from '@/components/builders/_lib/JsonEditor';
import { useMemo } from 'react';

interface ModalProps {
    implementationMap: Map<string, ImplementationMetaJson>;
    stepKind: StepConst | 'branch-case' | null;
    resources: ResourceMetaActiveJson[];
    onSelectResource: (resource: ResourceMetaActiveJson) => void;
    jsonText: string;
    setJsonText: (text: string) => void;
    jsonParseError: string | null;
    setJsonParseError: (error: string | null) => void;
    onConfirm: (json: string) => void;
    onCancel: () => void;
}
export default function Modal({ implementationMap, stepKind, resources, onSelectResource, jsonText, setJsonText, jsonParseError, setJsonParseError, onConfirm, onCancel }: ModalProps) {

    const availablePredicateImplementations = useMemo(() => Array.from(implementationMap.values()), [implementationMap]).filter(implementation => implementation.isPredicate); // ATTENTION: isPredicate lives on the Signature level

    return stepKind === CONSTANTS.STEP.for ? (
        <div
            className="absolute z-10 top-12 left-1/2 -translate-x-1/2 w-64 max-h-56 overflow-auto bg-white border border-gray-200 rounded shadow-lg"
            onClick={(e) => e.stopPropagation()}
        >
            <div className="sticky top-0 bg-gray-50 text-[11px] text-gray-600 px-2 py-1">Choose a resource</div>
            <ul className="py-1">
                {resources.map((resource, i) => (
                    <li key={i}>
                        <button
                            className="w-full text-left px-3 py-2 text-sm hover:bg-green-50"
                            onClick={(e) => {
                                e.stopPropagation();
                                onSelectResource(resource);
                                onConfirm('');
                            }}
                        >
                            {resource.exposedData.semanticIdentity}
                        </button>
                    </li>
                ))}
            </ul>
            <div className="border-t text-[11px] text-gray-500 px-2 py-1">Right-click input to clear</div>
        </div>
    ) : (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded shadow-lg max-w-2xl w-full p-6 m-4">
                <h3 className="text-lg font-medium mb-3">
                    {stepKind === CONSTANTS.STEP.while
                        ? 'Enter While condition (JSON)'
                        : stepKind === CONSTANTS.STEP.for
                            ? 'Enter For condition (JSON)'
                            : stepKind === CONSTANTS.STEP.branch
                                ? 'Enter Branch condition (JSON)'
                                : stepKind === 'branch-case'
                                    ? 'Enter Branch case condition (JSON)'
                                    : 'Enter Condition (JSON)'}
                </h3>
                <JsonEditor
                    legend="Condition"
                    valueText={jsonText}
                    onChangeText={(v) => {
                        setJsonText(v);
                        setJsonParseError(null);
                    }}
                    parseError={jsonParseError}
                    heightClass="h-48"
                />
                <div className="mt-4 flex justify-end gap-2">
                    <button className="px-3 py-2 rounded border" onClick={onCancel}>
                        Cancel
                    </button>
                    <button
                        className="px-3 py-2 rounded bg-blue-600 text-white"
                        onClick={() => onConfirm(jsonText)}
                    >
                        OK
                    </button>
                </div>
            </div>
        </div>
    );
}