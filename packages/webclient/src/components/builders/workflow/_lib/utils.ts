import type { Execution<PERSON><PERSON>, ResourceMapJson } from '@toolproof/_schemas';


export function getUnboundInputs(execution: ExecutionJson, resourceMap: ResourceMapJson): string[] { // ATTENTION: RoleIdJson?
    const inputBindings = execution.resourceBindings?.inputBindings ?? {};
    const unboundKeys: string[] = [];

    for (const [roleKey, bindingTarget] of Object.entries(inputBindings)) {
        // If the resourceMap does not have an entry for the binding target,
        // consider this input (roleKey) unbound.
        if (!(bindingTarget in resourceMap)) {
            unboundKeys.push(roleKey);
        }
    }

    return unboundKeys;
}


export function getUnboundOutputs(execution: ExecutionJson, resourceMap: ResourceMapJson): string[] { // ATTENTION: RoleIdJson?
    const outputBindings = execution.resourceBindings?.outputBindings ?? {};
    const unboundKeys: string[] = [];

    for (const [roleKey, bindingTarget] of Object.entries(outputBindings)) {
        // If the resourceMap does not have an entry for the binding target,
        // consider this output (roleKey) unbound.
        if (!(bindingTarget in resourceMap)) {
            unboundKeys.push(roleKey);
        }
    }

    return unboundKeys;
}


export function bindOutputs(execution: ExecutionJson): ResourceMapJson {
    const boundOutputs: ResourceMapJson = {};

    const outputs = execution.resourceBindings?.outputBindings ?? {};
    for (const [roleId, outputBindingId] of Object.entries(outputs)) {
        boundOutputs[outputBindingId] = { // ATTENTION
            id: outputBindingId, // ATTENTION: is this correct?
            path: '',
            timestamp: '',
            exposedData: {}
        }
    }

    return boundOutputs;
}
