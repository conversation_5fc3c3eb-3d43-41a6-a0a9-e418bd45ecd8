'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, SignatureMetaJson, ImplementationRolesJson, ImplementationDataJson } from '@toolproof/_schemas';
import type { UploadResult } from '@/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { validatePrimitive } from '@toolproof/_validator';
import { getUiContext } from '@/components/builders/_lib/utils';
import { PrimitivesSelector } from '@/components/builders/_lib/PrimitivesSelector';
import { LabeledInput } from '@/components/builders/_lib/LabeledInput';
import { ValidationErrors } from '@/components/builders/_lib/ValidationErrors';
import SaveControls from '@/components/builders/_lib/SaveControls';
import ReadOnlyIdField from '@/components/builders/_lib/ReadOnlyIdField';
import LabeledCheckbox from '@/components/builders/_lib/LabeledCheckbox';
import { useMeta } from '@/_lib/client/firebaseWebHelpers';
import { getNewId, uploadPrimitive } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';


export default function ImplementationBuilder() {
    const [id, setId] = useState('');
    const [name, setName] = useState('');
    const [inputs, setInputs] = useState<ImplementationRolesJson>({});
    const [outputs, setOutputs] = useState<ImplementationRolesJson>({});
    const [uri, setUri] = useState<string>('');
    const { items: signaturesMeta, loading: signaturesMetaLoading, error: signaturesMetaError } = useMeta<SignatureMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.signatures, false);
    const { items: typesMeta, loading: typesMetaLoading, error: typesMetaError } = useMeta<TypeMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.types, false);
    const [selectedSignatureId, setSelectedSignatureId] = useState<string>('');
    const [isPredicate, setIsPredicate] = useState<boolean>(false);
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);

    // DOC: Let an instance of the primitive be defined by state variables
    // ATTENTION: consider 'additionalProperties: false' on the schema
    const implementation: ImplementationDataJson = useMemo(
        () => ({
            id,
            name,
            signatureId: selectedSignatureId,
            isPredicate,
            roles: {
                inputs,
                outputs,
            }
        }),
        [id, name, selectedSignatureId, isPredicate, inputs, outputs],
    );

    // DOC: Fetch a new id for the primitive
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.PRIMITIVES.implementations);
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: When dependent primitives arrive, auto-select the first
    useEffect(() => {
        if (!selectedSignatureId && signaturesMeta.length) {
            setSelectedSignatureId(signaturesMeta[0].id);
        }
    }, [signaturesMeta, selectedSignatureId]);

    // DOC: Display errors from loading dependent primitives
    useEffect(() => {
        if (signaturesMetaError) setError(signaturesMetaError?.message ?? String(signaturesMetaError));
    }, [signaturesMetaError]);

    // DOC: Validate the primitive locally
    const rolesComplete = (roles: ImplementationRolesJson) =>
        Object.values(roles).every((r) => Boolean(r?.name?.trim() && r?.description?.trim() && r?.typeId?.trim()));
    const isValidLocal = Boolean(
        id.trim() &&
        name.trim() &&
        selectedSignatureId.trim() &&
        rolesComplete(inputs) &&
        rolesComplete(outputs)
    );

    const uiContext = getUiContext();

    // DOC: Validate the primitive formally against its schema
    const { isValid: isValidFormal, errors } = validatePrimitive(implementation, CONSTANTS.SCHEMA.ImplementationData, uiContext);

    // console.log('ImplementationBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal && isValidFormal;

    // DOC: Seed Implementation roles from selected signature, preserving user edits
    const selectedSignature = useMemo(
        () => signaturesMeta.find((s) => s.id === selectedSignatureId),
        [signaturesMeta, selectedSignatureId]
    );

    useEffect(() => {
        if (!selectedSignature) return;
        const sigInputs = (selectedSignature.roles?.inputs ?? {}) as Record<string, string>;
        const sigOutputs = (selectedSignature.roles?.outputs ?? {}) as Record<string, string>;

        setInputs((prev) => {
            const next: ImplementationRolesJson = {};
            for (const [roleId, typeId] of Object.entries(sigInputs)) {
                const existing = prev[roleId];
                next[roleId] = {
                    typeId,
                    name: existing?.name ?? '',
                    description: existing?.description ?? '',
                };
            }
            return next;
        });

        setOutputs((prev) => {
            const next: ImplementationRolesJson = {};
            for (const [roleId, typeId] of Object.entries(sigOutputs)) {
                const existing = prev[roleId];
                next[roleId] = {
                    typeId,
                    name: existing?.name ?? '',
                    description: existing?.description ?? '',
                };
            }
            return next;
        });
    }, [selectedSignature]);

    // DOC: Upload the primitive upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        const res = (await uploadPrimitive(CONSTANTS.PRIMITIVES.implementations, implementation, [], { uri })) as UploadResult;
        if (res?.ok) {
            const { docId } = res;
            setSaveStatus(`Saved. Firestore doc: ${docId}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div className='p-6 max-w-5xl mx-auto space-y-4'>
            <form id='implementation-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'id' is generated server-side */}
                    <ReadOnlyIdField value={id} />
                </div>

                <PrimitivesSelector
                    items={signaturesMeta}
                    value={selectedSignatureId}
                    label='Signature'
                    loading={signaturesMetaLoading}
                    onChange={(newSignatureId) => {
                        setSelectedSignatureId(newSignatureId);
                    }}
                />
                <LabeledInput
                    label='Name'
                    value={name}
                    onChange={setName}
                    placeholder={''}
                // error={isValidLocal.errors.name}
                />

                <LabeledInput
                    label='Uri'
                    value={uri}
                    onChange={setUri}
                    placeholder={''}
                // error={isValidLocal.errors.uri}
                />

                <LabeledCheckbox id='isPredicate' label='isPredicate' checked={isPredicate} onChange={setIsPredicate} />

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    {/* Inputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Inputs</legend>
                        {signaturesMetaLoading ? (
                            <div className='text-sm text-gray-500'>Loading signature…</div>
                        ) : (
                            <ul className='space-y-3'>
                                {Object.entries(inputs).length === 0 ? (
                                    <li className='text-sm text-gray-500'>No input roles in signature</li>
                                ) : (
                                    Object.entries(inputs).map(([roleId, role]) => {
                                        const typeLabel = typesMeta.find((t) => t.id === role.typeId)?.name || role.typeId;
                                        return (
                                            <li key={roleId} className='border rounded p-2'>
                                                <div className='text-xs text-gray-500 mb-2'>RoleId: {roleId}</div>
                                                <div className='text-xs text-gray-600 mb-2'>Type: {typeLabel}</div>
                                                <div className='grid grid-cols-1 gap-2'>
                                                    <input
                                                        type='text'
                                                        className='rounded border border-gray-300 px-3 py-2'
                                                        placeholder='Name'
                                                        value={role.name}
                                                        onChange={(e) => setInputs((prev) => ({
                                                            ...prev,
                                                            [roleId]: { ...prev[roleId], name: e.target.value }
                                                        }))}
                                                    />
                                                    <textarea
                                                        className='rounded border border-gray-300 px-3 py-2'
                                                        placeholder='Description'
                                                        value={role.description}
                                                        onChange={(e) => setInputs((prev) => ({
                                                            ...prev,
                                                            [roleId]: { ...prev[roleId], description: e.target.value }
                                                        }))}
                                                    />
                                                </div>
                                            </li>
                                        );
                                    })
                                )}
                            </ul>
                        )}
                    </fieldset>

                    {/* Outputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Outputs</legend>
                        {signaturesMetaLoading ? (
                            <div className='text-sm text-gray-500'>Loading signature…</div>
                        ) : (
                            <ul className='space-y-3'>
                                {Object.entries(outputs).length === 0 ? (
                                    <li className='text-sm text-gray-500'>No output roles in signature</li>
                                ) : (
                                    Object.entries(outputs).map(([roleId, role]) => {
                                        const typeLabel = typesMeta.find((t) => t.id === role.typeId)?.name || role.typeId;
                                        return (
                                            <li key={roleId} className='border rounded p-2'>
                                                <div className='text-xs text-gray-500 mb-2'>RoleId: {roleId}</div>
                                                <div className='text-xs text-gray-600 mb-2'>Type: {typeLabel}</div>
                                                <div className='grid grid-cols-1 gap-2'>
                                                    <input
                                                        type='text'
                                                        className='rounded border border-gray-300 px-3 py-2'
                                                        placeholder='Name'
                                                        value={role.name}
                                                        onChange={(e) => setOutputs((prev) => ({
                                                            ...prev,
                                                            [roleId]: { ...prev[roleId], name: e.target.value }
                                                        }))}
                                                    />
                                                    <textarea
                                                        className='rounded border border-gray-300 px-3 py-2'
                                                        placeholder='Description'
                                                        value={role.description}
                                                        onChange={(e) => setOutputs((prev) => ({
                                                            ...prev,
                                                            [roleId]: { ...prev[roleId], description: e.target.value }
                                                        }))}
                                                    />
                                                </div>
                                            </li>
                                        );
                                    })
                                )}
                            </ul>
                        )}
                    </fieldset>
                </div>
            </form>

            <div>
                <h3 className='font-semibold mb-2'>Preview {id}</h3>
                <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>{loadingPreview
                    ? 'Loading…'
                    : JSON.stringify(
                        implementation,
                        null,
                        2)}
                </pre>
                <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
            </div>

            <SaveControls
                formId='implementation-form'
                buttonText='Save Implementation'
                disabled={!isValid || !id}
                isValid={isValid}
                invalidMessage='Fill all fields before saving.'
                error={error}
                saveStatus={saveStatus}
            />
        </div>
    );
}
