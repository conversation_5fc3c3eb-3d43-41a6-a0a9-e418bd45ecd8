
export type JsonEditorProps = {
  legend?: string;
  valueText: string;
  onChangeText: (v: string) => void;
  parseError: string | null;
  heightClass?: string; // Tailwind height utility, e.g., 'h-64'
};

export function JsonEditor(props: JsonEditorProps) {
  const { legend, valueText, onChangeText, parseError, heightClass = 'h-64' } = props;

  const formatJson = () => {
    try {
      const parsed = JSON.parse(valueText);
      const pretty = JSON.stringify(parsed, null, 2);
      onChangeText(pretty);
    } catch {
      // parent shows parseError; ignore here
    }
  };

  return (
    <fieldset className='border rounded p-3'>
      <legend className='text-sm font-medium px-1'>{legend ?? 'JSON'}</legend>
      <div className='flex justify-end mb-2'>
        <button
          type='button'
          className='text-sm px-2 py-1 border rounded hover:bg-gray-50'
          onClick={formatJson}
          title='Pretty-print JSON with indentation'
        >
          Format JSON
        </button>
      </div>
      <textarea
        className={`font-mono w-full rounded border border-gray-300 p-3 ${heightClass}`}
        value={valueText}
        onChange={(e) => onChangeText(e.target.value)}
        spellCheck={false}
      />
      {parseError && (
        <div className='text-red-600 text-sm mt-2'>Invalid JSON: {parseError}</div>
      )}
    </fieldset>
  );
}
