import type { ErrorObject } from 'ajv';

export function ValidationErrors(props: { errors: ErrorObject[] | null | undefined }) {
  const { errors } = props;
  if (!errors || errors.length === 0) return null;
  return (
    <div className='mt-3 text-sm text-red-700'>
      <div className='font-semibold mb-1'>Schema validation errors:</div>
      <ul className='list-disc ml-5'>
        {errors.map((e, i) => (
          <li key={i}>{formatAjvError(e)}</li>
        ))}
      </ul>
    </div>
  );
}

function formatAjvError(e: ErrorObject): string {
  const where = e.instancePath || '(root)';
  return `${where}: ${e.message ?? 'Invalid'}`;
}
