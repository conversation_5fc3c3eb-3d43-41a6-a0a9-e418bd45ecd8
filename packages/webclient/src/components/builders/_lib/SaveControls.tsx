'use client';

export type SaveControlsProps = {
    formId: string;
    buttonText: string;
    disabled: boolean;
    isValid: boolean;
    invalidMessage?: string;
    error?: string | null;
    saveStatus?: string | null;
    className?: string;
};

export default function SaveControls({
    formId,
    buttonText,
    disabled,
    isValid,
    invalidMessage,
    error,
    saveStatus,
    className,
}: SaveControlsProps) {
    return (
        <div className={`flex gap-2 items-center ${className ?? ''}`}>
            <button
                form={formId}
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
                disabled={disabled}
            >
                {buttonText}
            </button>
            {!isValid && invalidMessage && (
                <span className="text-sm text-red-600">{invalidMessage}</span>
            )}
            {error && <span className="text-sm text-red-600">{error}</span>}
            {saveStatus && <span className="text-sm ml-2">{saveStatus}</span>}
        </div>
    );
}
