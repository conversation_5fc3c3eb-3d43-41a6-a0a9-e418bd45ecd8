'use client';

import type { TypeMeta<PERSON>son, SignatureRolesJson, SignatureDataJson } from '@toolproof/_schemas';
import type { UploadResult } from '@/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { validatePrimitive } from '@toolproof/_validator';
import { getUiContext } from '@/components/builders/_lib/utils';
import { ValidationErrors } from '@/components/builders/_lib/ValidationErrors';
import SaveControls from '@/components/builders/_lib/SaveControls';
import ReadOnlyIdField from '@/components/builders/_lib/ReadOnlyIdField';
import LabeledCheckbox from '@/components/builders/_lib/LabeledCheckbox';
import { useMeta } from '@/_lib/client/firebaseWebHelpers';
import { getNewId, uploadPrimitive } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';


export default function SignatureBuilder() {
    const [id, setId] = useState('');
    const [inputs, setInputs] = useState<SignatureRolesJson>({});
    const [outputs, setOutputs] = useState<SignatureRolesJson>({});
    const { items: typesMeta, loading: typesMetaLoading, error: typesMetaError } = useMeta<TypeMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.types, false);
    const [isPredicate, setIsPredicate] = useState<boolean>(false);
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [newInputTypeId, setNewInputTypeId] = useState<string>('');
    const [newOutputTypeId, setNewOutputTypeId] = useState<string>('');

    // DOC: Let an instance of the primitive be defined by state variables
    const signature: SignatureDataJson = useMemo(
        () => ({
            id,
            formatId: CONSTANTS.SPECIALS.FORMAT_ApplicationJob,
            isPredicate,
            roles: {
                inputs,
                outputs,
            }
        }),
        [id, isPredicate, inputs, outputs],
    );

    // DOC: Fetch a new id for the primitive
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.PRIMITIVES.signatures);
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: Display errors from loading dependent primitives
    useEffect(() => {
        if (typesMetaError) setError(typesMetaError?.message ?? String(typesMetaError));
    }, [typesMetaError]);

    const uiContext = getUiContext();

    // DOC: Validate the primitive formally against its schema
    const { isValid: isValidFormal, errors } = validatePrimitive(signature, CONSTANTS.SCHEMA.SignatureData, uiContext);

    // console.log('SignatureBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidFormal;

    // DOC: Initialize default selections for add-dropdowns when types load
    useEffect(() => {
        if (!typesMetaLoading && typesMeta?.length) {
            setNewInputTypeId((prev) => prev || typesMeta[0].id);
            setNewOutputTypeId((prev) => prev || typesMeta[0].id);
        }
    }, [typesMetaLoading, typesMeta]);

    // DOC: Add/Remove/Update role helpers for inputs and outputs
    const addRole = async (
        current: SignatureRolesJson,
        setCurrent: React.Dispatch<React.SetStateAction<SignatureRolesJson>>,
        typeId: string,
    ) => {
        const roleId = await getNewId(CONSTANTS.PRIMITIVES.roles);
        setCurrent({ ...current, [roleId]: typeId });
    };

    const updateRole = (
        current: SignatureRolesJson,
        setCurrent: React.Dispatch<React.SetStateAction<SignatureRolesJson>>,
        roleId: string,
        typeId: string,
    ) => {
        setCurrent({ ...current, [roleId]: typeId });
    };

    const removeRole = (
        current: SignatureRolesJson,
        setCurrent: React.Dispatch<React.SetStateAction<SignatureRolesJson>>,
        roleId: string,
    ) => {
        const { [roleId]: _removed, ...rest } = current;
        setCurrent(rest);
    };

    // DOC: Upload the primitive upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        const res = (await uploadPrimitive(CONSTANTS.PRIMITIVES.signatures, signature, [], {})) as UploadResult;
        if (res?.ok) {
            const { docId } = res;
            setSaveStatus(`Saved. Firestore doc: ${docId}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div className='p-6 max-w-5xl mx-auto space-y-4'>
            <form id='signature-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'id' is generated server-side */}
                    <ReadOnlyIdField value={id} />
                </div>

                <LabeledCheckbox id='isPredicate' label='isPredicate' checked={isPredicate} onChange={setIsPredicate} />

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    {/* Inputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Inputs</legend>
                        {typesMetaLoading ? (
                            <div className='text-sm text-gray-500'>Loading types…</div>
                        ) : (
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newInputTypeId}
                                        onChange={(e) => setNewInputTypeId(e.target.value)}
                                    >
                                        {typesMeta.map((t) => (
                                            <option key={t.id} value={t.id}>
                                                {t.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-blue-600 text-white rounded disabled:opacity-50'
                                        disabled={!newInputTypeId}
                                        onClick={() => addRole(inputs, setInputs, newInputTypeId)}
                                    >
                                        Add input
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(inputs).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No inputs yet</li>
                                    ) : (
                                        Object.entries(inputs).map(([roleId, typeId]) => (
                                            <li key={roleId} className='p-2 flex items-center gap-2'>
                                                <span className='text-xs text-gray-500 shrink-0'>{roleId}</span>
                                                <select
                                                    className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                    value={typeId}
                                                    onChange={(e) => updateRole(inputs, setInputs, roleId, e.target.value)}
                                                >
                                                    {typesMeta.map((t) => (
                                                        <option key={t.id} value={t.id}>
                                                            {t.name}
                                                        </option>
                                                    ))}
                                                </select>
                                                <button
                                                    type='button'
                                                    className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300'
                                                    onClick={() => removeRole(inputs, setInputs, roleId)}
                                                >
                                                    Remove
                                                </button>
                                            </li>
                                        ))
                                    )}
                                </ul>
                            </>
                        )}
                    </fieldset>

                    {/* Outputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Outputs</legend>
                        {typesMetaLoading ? (
                            <div className='text-sm text-gray-500'>Loading types…</div>
                        ) : (
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newOutputTypeId}
                                        onChange={(e) => setNewOutputTypeId(e.target.value)}
                                    >
                                        {typesMeta.map((t) => (
                                            <option key={t.id} value={t.id}>
                                                {t.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-blue-600 text-white rounded disabled:opacity-50'
                                        disabled={!newOutputTypeId}
                                        onClick={() => addRole(outputs, setOutputs, newOutputTypeId)}
                                    >
                                        Add output
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(outputs).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No outputs yet</li>
                                    ) : (
                                        Object.entries(outputs).map(([roleId, typeId]) => (
                                            <li key={roleId} className='p-2 flex items-center gap-2'>
                                                <span className='text-xs text-gray-500 shrink-0'>{roleId}</span>
                                                <select
                                                    className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                    value={typeId}
                                                    onChange={(e) => updateRole(outputs, setOutputs, roleId, e.target.value)}
                                                >
                                                    {typesMeta.map((t) => (
                                                        <option key={t.id} value={t.id}>
                                                            {t.name}
                                                        </option>
                                                    ))}
                                                </select>
                                                <button
                                                    type='button'
                                                    className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300'
                                                    onClick={() => removeRole(outputs, setOutputs, roleId)}
                                                >
                                                    Remove
                                                </button>
                                            </li>
                                        ))
                                    )}
                                </ul>
                            </>
                        )}
                    </fieldset>
                </div>
            </form>

            <div>
                <h3 className='font-semibold mb-2'>Preview {id}</h3>
                <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>{loadingPreview
                    ? 'Loading…'
                    : JSON.stringify(
                        signature,
                        null,
                        2)}
                </pre>
                <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
            </div>

            <SaveControls
                formId='signature-form'
                buttonText='Save Signature'
                disabled={!isValid || !id}
                isValid={isValid}
                invalidMessage='Fill all fields before saving.'
                error={error}
                saveStatus={saveStatus}
            />
        </div>
    );
}
