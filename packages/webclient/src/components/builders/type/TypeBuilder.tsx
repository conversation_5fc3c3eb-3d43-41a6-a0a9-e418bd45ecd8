'use client';

import type { FormatMeta<PERSON>son, ExposedSchemaJson, TypeDataJson } from '@toolproof/_schemas';
import type { UploadResult } from '@/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { validatePrimitive, validateResource } from '@toolproof/_validator';
import { getUiContext } from '@/components/builders/_lib/utils';
import { PrimitivesSelector } from '@/components/builders/_lib/PrimitivesSelector';
import { JsonEditor } from '@/components/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/components/builders/_lib/ValidationErrors';
import { LabeledInput } from '@/components/builders/_lib/LabeledInput';
import ReadOnlyIdField from '@/components/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/components/builders/_lib/SaveControls';
import { getNewId, uploadPrimitive } from '@/_lib/server/firebaseAdminHelpers';
import { useMeta } from '@/_lib/client/firebaseWebHelpers';
import type { ErrorObject } from 'ajv';
import { useMemo, useState, useEffect } from 'react';


const defaultExposedSchema: ExposedSchemaJson = {
    $schema: 'https://json-schema.org/draft/2020-12/schema',
    type: 'object',
    properties: {
        semanticIdentity: { type: 'integer' },
    },
    required: ['semanticIdentity'],
    additionalProperties: false,
};

const defaultType: TypeDataJson = {
    id: '',
    name: 'Integer',
    description: 'Represents an integer value.',
    formatId: '',
    exposedSchema: defaultExposedSchema,
};

const defaultSampleResource = { semanticIdentity: 0 };

export default function TypeBuilder() {
    const [id, setId] = useState<string>(defaultType.id);
    const [name, setName] = useState(defaultType.name);
    // const [description, setDescription] = useState(defaultType.description);
    const { items: formatsMeta, loading: formatsMetaLoading, error: formatsMetaError } = useMeta<FormatMetaJson>(CONSTANTS.STORAGE.primitives, CONSTANTS.PRIMITIVES.formats, true);
    const [selectedFormatId, setSelectedFormatId] = useState<string>(defaultType.formatId as string);
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);

    const [exposedSchema, setExposedSchema] = useState<ExposedSchemaJson>(defaultType.exposedSchema);
    const [exposedSchemaText, setExposedSchemaText] = useState<string>(
        JSON.stringify(defaultType.exposedSchema, null, 2)
    );
    const [exposedSchemaParseError, setExposedSchemaParseError] = useState<string | null>(null);

    const [sampleResource, setSampleResource] = useState<unknown>(defaultSampleResource);
    const [sampleResourceText, setSampleResourceText] = useState<string>(JSON.stringify(defaultSampleResource, null, 2));
    const [sampleResourceParseError, setSampleResourceParseError] = useState<string | null>(null);

    // DOC: Let an instance of the primitive be defined by state variables
    const type: TypeDataJson = useMemo(
        () => ({
            id,
            name,
            description: 'dummy-description',
            formatId: selectedFormatId,
            exposedSchema,
        }),
        [id, name, selectedFormatId, exposedSchema]
    );

    // DOC: Fetch a new id for the primitive
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.PRIMITIVES.types);
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: When dependent primitives arrive, auto-select the first
    useEffect(() => {
        if (!selectedFormatId && formatsMeta.length) {
            setSelectedFormatId(formatsMeta[0].id);
        }
    }, [formatsMeta, selectedFormatId]);

    // DOC: Display errors from loading dependent primitives
    useEffect(() => {
        if (formatsMetaError) setError(formatsMetaError?.message ?? String(formatsMetaError));
    }, [formatsMetaError]);

    // DOC: Validate the primitive locally
    const isValidLocal = useMemo(() => validateLocally(type), [type]);

    const uiContext = getUiContext();

    // DOC: Validate the primitive formally against its schema
    const { isValid: isValidFormal, errors } = validatePrimitive(type, CONSTANTS.SCHEMA.TypeData, uiContext);

    // console.log('-----------');
    console.log('TypeBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal && !exposedSchemaParseError && isValidFormal;

    // DOC: Validate sampleResource against exposedSchema
    const { isValid: isValidSample, errors: errorsSample } = validateResource(exposedSchema, sampleResource);

    // DOC: Update exposedSchema state on text change, with parse error handling
    const handleExposedSchemaChange = (text: string) => {
        setExposedSchemaText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setExposedSchemaParseError('exposedSchema must be a JSON Schema object.');
                return;
            }
            setExposedSchema(parsed as ExposedSchemaJson);
            setExposedSchemaParseError(null);
        } catch (e) {
            setExposedSchemaParseError((e as Error).message);
        }
    };

    // DOC: Update sampleResource state on text change, with parse error handling
    const handleSampleResourceChange = (text: string) => {
        setSampleResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setSampleResourceParseError('sampleResource must be a JSON object.');
                return;
            }
            setSampleResource(parsed);
            setSampleResourceParseError(null);
        } catch (e) {
            setSampleResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the primitive upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        // DOC: 'exposedSchema' is excluded from the primitive metadata saved to Firestore
        const res = (await uploadPrimitive(CONSTANTS.PRIMITIVES.types, type, ['exposedSchema'], {})) as UploadResult;
        if (res?.ok) {
            const { docId } = res;
            setSaveStatus(`Saved. Firestore doc: ${docId}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='type-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'id' is generated server-side */}
                    <ReadOnlyIdField value={id} />

                    <PrimitivesSelector
                        items={formatsMeta}
                        value={selectedFormatId}
                        label='Format'
                        loading={formatsMetaLoading}
                        onChange={(newFormatId) => {
                            setSelectedFormatId(newFormatId);
                        }}
                    />
                    <LabeledInput
                        label='Name'
                        value={name}
                        onChange={setName}
                        placeholder={defaultType.name}
                        error={isValidLocal.errors.name}
                    />
                    {/* <LabeledInput
                        label='Description'
                        value={description}
                        onChange={setDescription}
                        placeholder={defaultType.description}
                        error={isValidLocal.errors.description}
                    /> */}
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                        <JsonEditor
                            legend='exposedSchema (JSON Schema draft 2020-12)'
                            valueText={exposedSchemaText}
                            onChangeText={handleExposedSchemaChange}
                            parseError={exposedSchemaParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview {name}</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {loadingPreview
                                    ? 'Loading…'
                                    : JSON.stringify(
                                        type,
                                        null,
                                        2
                                    )}
                            </pre>
                            <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
                        </section>
                    </div>

                    <div>
                        <JsonEditor
                            legend='sampleResource (validated against exposedSchema)'
                            valueText={sampleResourceText}
                            onChangeText={handleSampleResourceChange}
                            parseError={sampleResourceParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview SampleResource</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {JSON.stringify(sampleResource, null, 2)}
                            </pre>
                            {errorsSample?.length ? (
                                <ValidationErrors errors={errorsSample} />
                            ) : (
                                <div className='text-sm text-green-700 mt-2'>
                                    SampleResource is valid against current exposedSchema.
                                </div>
                            )}
                        </section>
                    </div>
                </div>
            </form>

            <SaveControls
                formId='type-form'
                buttonText='Save Type'
                disabled={!isValid || !id}
                isValid={isValid}
                invalidMessage='Fix errors before saving.'
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}

function validateLocally(
    type: TypeDataJson
): { valid: boolean; errors: Record<string, string | undefined> } {
    const errors: Record<string, string | undefined> = {};

    // id is server-generated; no client-side validation
    if (!type.name.trim()) errors.name = 'name is required';
    // if (!rt.description?.trim()) errors.description = 'Description is required';
    if (!type.formatId) errors.formatId = 'formatId is required';
    if (!type.exposedSchema || typeof type.exposedSchema !== 'object' || Array.isArray(type.exposedSchema)) {
        errors.exposedSchema = 'exposedSchema must be a JSON Schema object';
    }

    return { valid: Object.keys(errors).length === 0, errors };
}
