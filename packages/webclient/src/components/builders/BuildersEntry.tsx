'use client';

import type { PrimitiveConst } from '@toolproof/_lib/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import FormatBuilder from '@/components/builders/format/FormatBuilder';
import TypeBuilder from '@/components/builders/type/TypeBuilder';
import SignatureBuilder from '@/components/builders/signature/SignatureBuilder';
import ImplementationBuilder from '@/components/builders/implementation/ImplementationBuilder';
import ResourceBuilder from './resource/ResourceBuilder';
import WorkflowBuilder from '@/components/builders/workflow/WorkflowBuilder';
import { useState } from 'react';

type BuilderKey = PrimitiveConst | typeof CONSTANTS.NON_PRIMITIVES.resources | typeof CONSTANTS.NON_PRIMITIVES.workflow;

const builders: { key: BuilderKey; label: string }[] = [
    { key: CONSTANTS.PRIMITIVES.formats, label: 'Format' },
    { key: CONSTANTS.PRIMITIVES.types, label: 'Type' },
    { key: CONSTANTS.PRIMITIVES.signatures, label: 'Signature' },
    { key: CONSTANTS.PRIMITIVES.implementations, label: 'Implementation' },
    { key: CONSTANTS.NON_PRIMITIVES.resources, label: 'Resource' },
    { key: CONSTANTS.NON_PRIMITIVES.workflow, label: 'Workflow' },
];

export default function BuildersEntry() {
    const [active, setActive] = useState<BuilderKey>(CONSTANTS.PRIMITIVES.formats);

    return (
        <div className="p-4">
            <h1 className="text-2xl font-semibold mb-4">Builders</h1>

            {/* Top controls: Primitives group on the left, Workflow on the right */}
            <div className="flex items-center justify-between flex-wrap gap-3 mb-6">
                {/* Left: Primitives label + first four buttons */}
                <div className="flex items-center gap-2 flex-wrap">
                    <span className="text-xs font-medium uppercase tracking-wide text-gray-500 mr-1">Primitives</span>
                    {([CONSTANTS.PRIMITIVES.formats, CONSTANTS.PRIMITIVES.types, CONSTANTS.PRIMITIVES.signatures, CONSTANTS.PRIMITIVES.implementations, 'resources'] as BuilderKey[]).map((key) => {
                        const label = builders.find((b) => b.key === key)?.label ?? key;
                        return (
                            <button
                                key={key}
                                onClick={() => setActive(key)}
                                className={`px-3 py-1.5 rounded border text-sm transition-colors ${active === key
                                    ? 'bg-blue-600 text-white border-blue-600'
                                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                    }`}
                                aria-pressed={active === key}
                            >
                                {label}
                            </button>
                        );
                    })}
                </div>

                {/* Right: Workflow button */}
                <div className="flex items-center">
                    <button
                        onClick={() => setActive('workflow')}
                        className={`px-3 py-1.5 rounded border text-sm transition-colors ${active === 'workflow'
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                            }`}
                        aria-pressed={active === 'workflow'}
                    >
                        Workflow
                    </button>
                </div>
            </div>

            <div className="bg-white rounded border border-gray-200 p-4">
                {active === CONSTANTS.PRIMITIVES.formats && <FormatBuilder />}
                {active === CONSTANTS.PRIMITIVES.types && <TypeBuilder />}
                {active === CONSTANTS.PRIMITIVES.signatures && <SignatureBuilder />}
                {active === CONSTANTS.PRIMITIVES.implementations && <ImplementationBuilder />}
                {active === CONSTANTS.NON_PRIMITIVES.resources && <ResourceBuilder />}
                {active === CONSTANTS.NON_PRIMITIVES.workflow && <WorkflowBuilder />}
            </div>
        </div>
    );
}
