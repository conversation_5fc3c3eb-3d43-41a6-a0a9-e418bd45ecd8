'use client';

import type { FormatMeta<PERSON>son, ExposedSchema<PERSON>son, TypeDataJson } from '@toolproof/_schemas';
import type { UploadResult } from '@/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { validateResource } from '@toolproof/_validator';
import { getUiContext } from '@/components/builders/_lib/utils';
import { PrimitivesSelector } from '@/components/builders/_lib/PrimitivesSelector';
import { JsonEditor } from '@/components/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/components/builders/_lib/ValidationErrors';
import ReadOnlyIdField from '@/components/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/components/builders/_lib/SaveControls';
import { getNewId, writeToCAFS } from '@/_lib/server/firebaseAdminHelpers';
import { usePrimitivesData } from '@/_lib/client/firebaseWebHelpers';
import type { ErrorObject } from 'ajv';
import { useMemo, useState, useEffect } from 'react';


export default function ResourceBuilder() {
    const { items: typesData, loading: typesLoading, error: typesError } = usePrimitivesData<TypeDataJson>(CONSTANTS.STORAGE.tp_primitives, CONSTANTS.PRIMITIVES.types);
    const [selectedType, setSelectedType] = useState<TypeDataJson>();
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [sampleResource, setSampleResource] = useState<unknown>('');
    const [sampleResourceText, setSampleResourceText] = useState<string>(JSON.stringify('', null, 2));
    const [sampleResourceParseError, setSampleResourceParseError] = useState<string | null>(null);
    const [jobStepId, setJobStepId] = useState<string>('');

    console.log('selectedType:', JSON.stringify(selectedType, null, 2));

    // DOC: Fetch a new id for the primitive
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!jobStepId) {
                const newId = await getNewId('JOBSTEP');
                setJobStepId(newId);
            }
        };
        asyncWrapper();
    }, [jobStepId]);


    // DOC: Validate sampleResource against exposedSchema (guard when no type selected yet)
    const { isValid, errors: errors } = useMemo(() => {
        if (!selectedType?.exposedSchema) {
            return { isValid: false, errors: null as ErrorObject[] | null };
        }
        return validateResource(selectedType.exposedSchema, sampleResource);
    }, [selectedType?.exposedSchema, sampleResource]);


    // DOC: Update sampleResource state on text change, with parse error handling
    const handleSampleResourceChange = (text: string) => {
        setSampleResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setSampleResourceParseError('sampleResource must be a JSON object.');
                return;
            }
            setSampleResource(parsed);
            setSampleResourceParseError(null);
        } catch (e) {
            setSampleResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the resource upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        if (!selectedType) return;
        const resourceId = `${CONSTANTS.SPECIALS.ROLE_ManualCreation}__${jobStepId}`;
        const res = (await writeToCAFS(selectedType.id, resourceId, JSON.stringify(sampleResource, null, 2)));
        if (res.success) {
            const { storagePath } = res;
            setSaveStatus(`Saved. GCS: ${storagePath}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='resource-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'jobStepId' is generated server-side */}
                    <ReadOnlyIdField value={jobStepId} />
                </div>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <PrimitivesSelector
                        items={typesData ?? []}
                        value={selectedType?.id ?? ''}
                        label='Type'
                        loading={typesLoading}
                        onChange={(newTypeId) => {
                            const newType = (typesData ?? []).find(type => type.id === newTypeId);
                            setSelectedType(newType);
                        }}
                    />

                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>

                    <div>
                        <JsonEditor
                            legend='sampleResource (select a Type to validate against its exposedSchema)'
                            valueText={sampleResourceText}
                            onChangeText={handleSampleResourceChange}
                            parseError={sampleResourceParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview SampleResource</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {JSON.stringify(sampleResource, null, 2)}
                            </pre>
                            {!selectedType ? (
                                <div className='text-sm text-gray-600 mt-2'>
                                    Select a Type to run schema validation.
                                </div>
                            ) : errors?.length ? (
                                <ValidationErrors errors={errors} />
                            ) : (
                                <div className='text-sm text-green-700 mt-2'>
                                    SampleResource is valid against current exposedSchema.
                                </div>
                            )}
                        </section>
                    </div>
                </div>
            </form>

            <SaveControls
                formId='resource-form'
                buttonText='Save Resource'
                disabled={!isValid}
                isValid={isValid}
                invalidMessage='Fix errors before saving.'
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}

function validateLocally(
    type: TypeDataJson
): { valid: boolean; errors: Record<string, string | undefined> } {
    const errors: Record<string, string | undefined> = {};

    // id is server-generated; no client-side validation
    if (!type.name.trim()) errors.name = 'name is required';
    // if (!rt.description?.trim()) errors.description = 'Description is required';
    if (!type.formatId) errors.formatId = 'formatId is required';
    if (!type.exposedSchema || typeof type.exposedSchema !== 'object' || Array.isArray(type.exposedSchema)) {
        errors.exposedSchema = 'exposedSchema must be a JSON Schema object';
    }

    return { valid: Object.keys(errors).length === 0, errors };
}
