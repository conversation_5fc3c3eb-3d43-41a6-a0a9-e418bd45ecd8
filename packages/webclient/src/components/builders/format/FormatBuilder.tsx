'use client';

import type { FormatDataJson } from '@toolproof/_schemas';
import type { UploadResult } from '@/types';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { validatePrimitive } from '@toolproof/_validator';
import { getUiContext } from '@/components/builders/_lib/utils';
import { LabeledInput } from '@/components/builders/_lib/LabeledInput';
import ReadOnlyIdField from '@/components/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/components/builders/_lib/SaveControls';
import { ValidationErrors } from '@/components/builders/_lib/ValidationErrors';
import { getNewId, uploadPrimitive } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';


export default function FormatBuilder() {
    const [id, setId] = useState<string>('');
    const [name, setName] = useState<string>('');
    // const [description, setDescription] = useState<string>('');
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);

    // DOC: Let an instance of the primitive be defined by state variables
    const format: FormatDataJson = useMemo(
        () => {
            return {
                id,
                name,
                // description,
            };
        },
        [id, name]
    );

    // DOC: Fetch a new id for the primitive
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.PRIMITIVES.formats);
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: Validate the primitive locally
    const isValidLocal = Boolean(id.trim() && name.trim());

    const uiContext = getUiContext();

    // DOC: Validate the primitive formally against its schema
    const { isValid: isValidFormal, errors } = validatePrimitive(format, CONSTANTS.SCHEMA.FormatData, uiContext);

    // console.log('-----------');
    // console.log('FormatBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal && isValidFormal;

    // DOC: Upload the primitive upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        const res = (await uploadPrimitive(CONSTANTS.PRIMITIVES.formats, format, [], {})) as UploadResult;
        if (res?.ok) {
            const { docId } = res;
            setSaveStatus(`Saved. Firestore doc: ${docId}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='format-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'id' is generated server-side */}
                    <ReadOnlyIdField value={id} />

                    <LabeledInput label='Name' value={name} onChange={setName} placeholder='Format name' />

                    {/* <LabeledInput label='Description' value={description} onChange={setDescription} placeholder='What this format is for' /> */}

                </div>

                <div>
                    <h3 className='font-semibold mb-2'>Preview {name}</h3>
                    <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                        {loadingPreview
                            ? 'Loading…'
                            : JSON.stringify(
                                format,
                                null,
                                2
                            )}
                    </pre>
                    <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
                </div>
            </form>

            <SaveControls
                formId='format-form'
                buttonText='Save Format'
                disabled={!isValid}
                isValid={isValid}
                invalidMessage='Fill all fields before saving.'
                error={error}
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}
