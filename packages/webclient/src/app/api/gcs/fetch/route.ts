export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

import type { NextRequest } from 'next/server';

const ALLOWED_BUCKETS = new Set<string>([
  'tp-resources',
  'toolproof-563fe.appspot.com',
]);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const bucket = searchParams.get('bucket');
    const object = searchParams.get('object');

    if (!bucket || !object) {
      return Response.json({ error: 'Missing bucket or object' }, { status: 400 });
    }
    if (!ALLOWED_BUCKETS.has(bucket)) {
      return Response.json({ error: 'Forbidden bucket' }, { status: 403 });
    }

    const mediaUrl = `https://storage.googleapis.com/${bucket}/${encodeURI(object)}`;
    const upstream = await fetch(mediaUrl, { headers: { Accept: 'application/json' }, cache: 'no-store' });
    const bodyText = await upstream.text();
    if (!upstream.ok) {
      return Response.json(
        { error: 'Upstream error', status: upstream.status, statusText: upstream.statusText, body: bodyText?.slice(0, 500) },
        { status: upstream.status }
      );
    }

    const contentType = upstream.headers.get('content-type') || 'application/json';
    return new Response(bodyText, { status: 200, headers: { 'Content-Type': contentType } });
  } catch (e) {
    return Response.json({ error: (e as Error)?.message || 'Proxy error' }, { status: 500 });
  }
}
