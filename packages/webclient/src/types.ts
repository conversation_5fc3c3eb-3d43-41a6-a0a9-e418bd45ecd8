import { ResourceMetaPassiveJson } from '@toolproof/_schemas';

export type UploadResult = { ok: boolean;  docId: string; error?: string };

// UI helper types (local)
export type SelectedIndex = {
	// Index into workflowSpec.workflow.steps
	stepIndex: number;
	// For BranchStep only; otherwise has no meaning
	caseIndex: number | null;
};

// ATTENTION
export type ExposedDataHack = { semanticIdentity: number };

// ATTENTION: if extractor functions were typed, we would know the shape of exposedData for each Type
export type ResourceMerged = ResourceMetaPassiveJson & { exposedData: ExposedDataHack };
