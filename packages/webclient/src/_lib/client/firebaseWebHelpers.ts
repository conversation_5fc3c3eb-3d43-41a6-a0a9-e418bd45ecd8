import type { Primitive<PERSON><PERSON><PERSON><PERSON>, Primitive<PERSON><PERSON><PERSON>son, TypeId<PERSON>son, ResourceMetaPassiveJson } from '@toolproof/_schemas';
import type { ResourceMerged } from '@/types';
import type { PrimitiveConst } from '@toolproof/_lib/types';
import * as CONSTANTS from '@toolproof/_lib/constants';
import { db } from '@/_lib/setup/firebaseWebInit';
import { collection, query, onSnapshot } from 'firebase/firestore';
import type { DocumentData, Query, QueryDocumentSnapshot } from 'firebase/firestore';
import { useCollection } from 'react-firebase-hooks/firestore';
import { useEffect, useMemo, useState } from 'react';

// ATTENTION: consider adding flag to exclude application/job
// ATTENTION: consider splitting into members and specials, alternatively have a isSpecial flag on the data
export const useMeta = <T extends PrimitiveMetaJson | ResourceMetaPassiveJson>(
  collectionName: string,
  subCollectionName: PrimitiveConst | TypeIdJson,
  includeSpecials: boolean
) => {
  // Memoize queries to avoid resubscribing on every render
  const membersQuery = useMemo(() => (
    query(collection(db, collectionName, subCollectionName, CONSTANTS.MEMBERS))
  ), [collectionName, subCollectionName]);

  const [membersSnap, membersLoading, membersError] = useCollection(membersQuery);

  const specialsQuery: Query<DocumentData> | undefined = useMemo(() => (
    includeSpecials
      ? query(collection(db, collectionName, subCollectionName, CONSTANTS.SPECIALS))
      : undefined
  ), [collectionName, subCollectionName, includeSpecials]);

  const [specialsSnap, specialsLoading, specialsError] = useCollection(specialsQuery ?? null);

  const items = useMemo(() => {
    const seen = new Set<string>();
    const result: T[] = [];

    const getKey = (_data: unknown, doc: QueryDocumentSnapshot<DocumentData>) => {
      // Use the full Firestore document path for uniqueness across subcollections
      // This avoids collapsing distinct docs that happen to share the same data.path value
      return doc.ref.path || doc.id;
    };

    const pushDoc = (doc: QueryDocumentSnapshot<DocumentData>) => {
      const data = doc.data() as T; // full document as-is
      const key = getKey(data as unknown, doc);
      if (!seen.has(key)) {
        seen.add(key);
        // Always include the Firestore document id alongside data
        result.push({ ...(data as object), id: doc.id } as T);
      }
    };

    (membersSnap?.docs || []).forEach(pushDoc);
    if (includeSpecials) (specialsSnap?.docs || []).forEach(pushDoc);

    return result;
  }, [membersSnap?.docs, specialsSnap?.docs, includeSpecials]);

  const loading = membersLoading || (includeSpecials && !!specialsQuery ? !!specialsLoading : false);
  const error = (membersError as Error | undefined) ?? (includeSpecials ? (specialsError as Error | undefined) : undefined);

  return { items, loading, error } as { items: T[]; loading: boolean; error: Error | undefined };
}

export const usePrimitivesData = <T extends PrimitiveDataJson>(
  bucketName: string,
  path: string
) => {
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  useEffect(() => {
    if (!bucketName || !path) return;
    const controller = new AbortController();
    const signal = controller.signal;

    async function listObjects(prefix: string) {
      const collected: string[] = [];
      let pageToken: string | undefined = undefined;
      const base = `https://storage.googleapis.com/storage/v1/b/${encodeURIComponent(bucketName)}/o`;

      do {
        const params = new URLSearchParams({ prefix });
        if (pageToken) params.set('pageToken', pageToken);
        params.set('fields', 'items(name),nextPageToken');

        const url = `${base}?${params.toString()}`;
        const res = await fetch(url, { signal });
        if (!res.ok) {
          throw new Error(`Failed to list objects: ${res.status} ${res.statusText}`);
        }
        const data: { items?: { name: string }[]; nextPageToken?: string } = await res.json();
        const names = (data.items || [])
          .map((i) => i.name)
          .filter((n): n is string => typeof n === 'string' && n.toLowerCase().endsWith('.json'));
        collected.push(...names);
        pageToken = data.nextPageToken;
      } while (pageToken && !signal.aborted);

      return collected;
    }

    async function fetchJson(objectName: string): Promise<T> {
      const url = `https://storage.googleapis.com/storage/v1/b/${encodeURIComponent(bucketName)}/o/${encodeURIComponent(objectName)}?alt=media`;
      const res = await fetch(url, { signal, headers: { Accept: 'application/json' } });
      if (!res.ok) {
        throw new Error(`Failed to fetch ${objectName}: ${res.status} ${res.statusText}`);
      }
      return (await res.json()) as T;
    }

    const run = async () => {
      try {
        setLoading(true);
        setError(undefined);
        setItems([]);

        const normalized = path.trim().replace(/^\/+/, '');
        if (normalized.toLowerCase().endsWith('.json')) {
          const json = await fetchJson(normalized);
          if (!signal.aborted) setItems([json]);
          return;
        }

        const prefix = normalized.endsWith('/') ? normalized : `${normalized}/`;
        const objectNames = await listObjects(prefix);
        if (signal.aborted) return;
        if (objectNames.length === 0) {
          setItems([]);
          return;
        }
        const results = await Promise.all(objectNames.map((name) => fetchJson(name)));
        if (!signal.aborted) setItems(results);
      } catch (e) {
        if (signal.aborted) return;
        setError(e as Error);
      } finally {
        if (!signal.aborted) setLoading(false);
      }
    };

    run();

    return () => controller.abort();
  }, [bucketName, path]);

  return { items, loading, error };
}

// ATTENTION: consider merging with usePrimitivesData
export const useResourcesData = <T>(
  bucketName: string,
  resourceId: string
) => {
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  useEffect(() => {
    if (!bucketName || !resourceId) return;
    const controller = new AbortController();
    const signal = controller.signal;

    async function listObjects(prefix: string) {
      const collected: string[] = [];
      let pageToken: string | undefined = undefined;
      const base = `https://storage.googleapis.com/storage/v1/b/${encodeURIComponent(bucketName)}/o`;

      do {
        const params = new URLSearchParams({ prefix });
        if (pageToken) params.set('pageToken', pageToken);
        params.set('fields', 'items(name),nextPageToken');

        const url = `${base}?${params.toString()}`;
        const res = await fetch(url, { signal });
        if (!res.ok) {
          throw new Error(`Failed to list objects: ${res.status} ${res.statusText}`);
        }
        const data: { items?: { name: string }[]; nextPageToken?: string } = await res.json();
        const names = (data.items || [])
          .map((i) => i.name)
        // .filter((n): n is string => typeof n === 'string' && n.toLowerCase().endsWith('.json')); // ATTENTION
        collected.push(...names);
        pageToken = data.nextPageToken;
      } while (pageToken && !signal.aborted);

      return collected;
    }

    async function fetchJson(objectName: string): Promise<T> {
      const url = `https://storage.googleapis.com/storage/v1/b/${encodeURIComponent(bucketName)}/o/${encodeURIComponent(objectName)}?alt=media`;
      const res = await fetch(url, { signal, headers: { Accept: 'application/json' } });
      if (!res.ok) {
        throw new Error(`Failed to fetch ${objectName}: ${res.status} ${res.statusText}`);
      }
      return (await res.json()) as T;
    }

    const run = async () => {
      try {
        setLoading(true);
        setError(undefined);
        setItems([]);

        const normalized = resourceId.trim().replace(/^\/+/, '');
        if (normalized.toLowerCase().endsWith('.json')) {
          const json = await fetchJson(normalized);
          if (!signal.aborted) setItems([json]);
          return;
        }

        const prefix = normalized.endsWith('/') ? normalized : `${normalized}/`;
        const objectNames = await listObjects(prefix);
        if (signal.aborted) return;
        if (objectNames.length === 0) {
          setItems([]);
          return;
        }
        const results = await Promise.all(objectNames.map((name) => fetchJson(name)));
        if (!signal.aborted) setItems(results);
      } catch (e) {
        if (signal.aborted) return;
        setError(e as Error);
      } finally {
        if (!signal.aborted) setLoading(false);
      }
    };

    run();

    return () => controller.abort();
  }, [bucketName, resourceId]);

  return { items, loading, error };
}

// Helper: return tuples of { objectName, data } under a given resourceId prefix
export const useResourcesDataNamed = <T>(
  bucketName: string,
  resourceId: string
) => {
  const [items, setItems] = useState<Array<{ objectName: string; data: T }>>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  useEffect(() => {
    if (!bucketName || !resourceId) return;
    const controller = new AbortController();
    const signal = controller.signal;

    async function listObjects(prefix: string) {
      const collected: string[] = [];
      let pageToken: string | undefined = undefined;
      const base = `https://storage.googleapis.com/storage/v1/b/${encodeURIComponent(bucketName)}/o`;

      do {
        const params = new URLSearchParams({ prefix });
        if (pageToken) params.set('pageToken', pageToken);
        params.set('fields', 'items(name),nextPageToken');

        const url = `${base}?${params.toString()}`;
        const res = await fetch(url, { signal });
        if (!res.ok) throw new Error(`Failed to list objects: ${res.status} ${res.statusText}`);
        const data: { items?: { name: string }[]; nextPageToken?: string } = await res.json();
        const names = (data.items || []).map((i) => i.name);
        collected.push(...names);
        pageToken = data.nextPageToken;
      } while (pageToken && !signal.aborted);

      return collected;
    }

    async function fetchJson(objectName: string): Promise<T> {
      const url = `https://storage.googleapis.com/storage/v1/b/${encodeURIComponent(bucketName)}/o/${encodeURIComponent(objectName)}?alt=media`;
      const res = await fetch(url, { signal, headers: { Accept: 'application/json' } });
      if (!res.ok) throw new Error(`Failed to fetch ${objectName}: ${res.status} ${res.statusText}`);
      return (await res.json()) as T;
    }

    const run = async () => {
      try {
        setLoading(true);
        setError(undefined);
        setItems([]);

        const normalized = resourceId.trim().replace(/^\/+/, '');
        const prefix = normalized.endsWith('/') ? normalized : `${normalized}/`;
        const objectNames = await listObjects(prefix);
        if (signal.aborted) return;
        const results = await Promise.all(
          objectNames.map(async (name) => ({ objectName: name, data: await fetchJson(name) }))
        );
        if (!signal.aborted) setItems(results);
      } catch (e) {
        if (signal.aborted) return;
        setError(e as Error);
      } finally {
        if (!signal.aborted) setLoading(false);
      }
    };

    run();
    return () => controller.abort();
  }, [bucketName, resourceId]);

  return { items, loading, error };
};

// ATTENTION: must dedupe metas by path when merging
// New: merged view of resources metadata + exposed data
// Returns array of { path, timestamp, exposedData }
export const useResourcesMerged = <T = unknown, TExposed = unknown>(
  collectionName: string,
  resourceId: TypeIdJson,
  bucketName: string = CONSTANTS.TP_RESOURCES
) => {
  // Reuse Firestore subscription for metadata
  // IMPORTANT: memoize query to avoid resubscribing every render
  const membersQuery = useMemo(() => (
    query(collection(db, collectionName, resourceId, CONSTANTS.MEMBERS))
  ), [collectionName, resourceId]);

  const [value, metaLoading, metaError] = useCollection(membersQuery);

  const [items, setItems] = useState<Array<T>>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  // Create a stable key representing the current docs content (path|timestamp) to avoid reruns from referential changes
  const metaDocsKey = useMemo(() => {
    const docs = value?.docs ?? [];
    return docs
      .map((d) => {
        const data = d.data() as { path: string; timestamp: string };
        return `${data.path}|${data.timestamp ?? ''}`;
      })
      .join('\n');
  }, [value]);

  useEffect(() => {
    const docs = value?.docs ?? [];
    if (!bucketName || docs.length === 0) {
      setItems([]);
      // reflect metadata loading/error without triggering refetches
      setLoading(metaLoading);
      setError(metaError as Error | undefined);
      return;
    }

    const controller = new AbortController();
    const { signal } = controller;

    async function fetchJson(objectName: string): Promise<TExposed> {
      const url = `https://storage.googleapis.com/storage/v1/b/${encodeURIComponent(bucketName)}/o/${encodeURIComponent(objectName)}?alt=media`;
      const res = await fetch(url, { signal, headers: { Accept: 'application/json' } });
      if (!res.ok) {
        throw new Error(`Failed to fetch ${objectName}: ${res.status} ${res.statusText}`);
      }
      return (await res.json()) as TExposed;
    }

    const run = async () => {
      try {
        setLoading(true);
        setError(undefined);
        // Read paths from metadata snapshot to drive fetching order
        const metas: ResourceMetaPassiveJson[] = docs.map((d) => {
          const data = d.data() as { id?: string; path: string; timestamp: string };
          const { path, timestamp } = data;
          const id = d.id;
          return { id, path, timestamp } as ResourceMetaPassiveJson;
        });
        const results = await Promise.all(
          metas.map(async (m) => {
            const exposed = await fetchJson(m.path);
            return { ...m, exposedData: exposed } as T;
          })
        );
        if (!signal.aborted) setItems(results);
      } catch (e) {
        if (signal.aborted) return;
        setError(e as Error);
      } finally {
        if (!signal.aborted) setLoading(false);
      }
    };

    run();
    return () => controller.abort();
    // We intentionally depend only on stable keys to avoid resubscribe/refetch loops
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bucketName, metaDocsKey]);

  return { items, loading: metaLoading || loading, error: (metaError as Error | undefined) ?? error };
};

export const useResourcesMerged2 = (
  collectionName: string,
  resourceIds: TypeIdJson[],
  bucketName: string = CONSTANTS.TP_RESOURCES,
  options?: { debug?: boolean }
) => {
  const debug = !!options?.debug;
  const resourceIdsKey = useMemo(() => {
    // Stable key for dependency arrays and subscriptions
    return [...resourceIds].sort().join('|');
  }, [resourceIds]);
  // Derive ids from key inside effects to avoid array-identity churn
  const getIdsFromKey = (key: string) => (key ? key.split('|').filter(Boolean) : []);
  // Metadata state per typeId
  const [metaByType, setMetaByType] = useState<Record<string, ResourceMetaPassiveJson[]>>({});
  const [metaLoading, setMetaLoading] = useState<boolean>(false);
  const [metaError, setMetaError] = useState<Error | undefined>(undefined);

  // Subscribe to multiple members subcollections dynamically (without calling hooks in a loop)
  useEffect(() => {
    const ids = getIdsFromKey(resourceIdsKey);
    if (debug) console.debug('[useResourcesMerged2] subscribe start', { collectionName, ids });
    // Reset only error/loading; do NOT clear meta map to avoid churn/empties
    setMetaError(undefined);
    setMetaLoading(ids.length > 0);

    if (!collectionName || ids.length === 0) {
      setMetaLoading(false);
      return;
    }

    let pending = ids.length;
    const unsubs: Array<() => void> = [];

    // Prune removed ids from meta map once per re-subscribe
    setMetaByType((prev) => {
      const next: Record<string, ResourceMetaPassiveJson[]> = {};
      for (const id of ids) {
        if (prev[id]) next[id] = prev[id];
      }
      return next;
    });

    ids.forEach((resourceId) => {
      const q = query(collection(db, collectionName, resourceId, CONSTANTS.MEMBERS));
      const unsub = onSnapshot(
        q,
        (snapshot) => {
          const metas: ResourceMetaPassiveJson[] = snapshot.docs.map((d) => {
            const data = d.data() as { id?: string; path: string; timestamp: string };
            const { path, timestamp } = data;
            const id = d.id;
            return { id, path, timestamp } as ResourceMetaPassiveJson;
          });
          if (debug) console.debug('[useResourcesMerged2] snapshot', { resourceId, count: metas.length });
          setMetaByType((prev) => ({ ...prev, [resourceId]: metas }));
          if (pending > 0) {
            pending -= 1;
            if (pending === 0) setMetaLoading(false);
          }
        },
        (err) => {
          if (debug) console.error('[useResourcesMerged2] snapshot error', { resourceId, error: (err as Error)?.message });
          setMetaError(err as Error);
          // Mark this id as present with empty metas so downstream logic can proceed
          setMetaByType((prev) => ({ ...prev, [resourceId]: [] }));
          if (pending > 0) {
            pending -= 1;
            if (pending === 0) setMetaLoading(false);
          }
        }
      );
      unsubs.push(unsub);
    });

    return () => {
      if (debug) console.debug('[useResourcesMerged2] unsubscribe all', { count: unsubs.length });
      unsubs.forEach((u) => u());
    };
  }, [collectionName, resourceIdsKey, debug]);

  // Stable key to represent current meta docs across all resource types
  const metaDocsKey = useMemo(() => {
    const ids = getIdsFromKey(resourceIdsKey);
    const parts: string[] = [];
    ids.forEach((id) => {
      const metas = metaByType[id] ?? [];
      const lines = metas
        .map((m) => `${m.path}|${m.timestamp ?? ''}`)
        .sort();
      parts.push(`${id}::${lines.join(',')}`);
    });
    return parts.join('\n');
  }, [metaByType, resourceIdsKey]);

  const [items, setItems] = useState<Record<string, ResourceMerged[]>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  // ...

  // Ensure items always has keys for all requested ids (even before data arrives)
  useEffect(() => {
    const ids = getIdsFromKey(resourceIdsKey);
    if (debug) console.debug('[useResourcesMerged2] ensure item keys', { ids });
    setItems((prev) => {
      const next: Record<string, ResourceMerged[]> = {};
      ids.forEach((id) => {
        next[id] = prev[id] ?? [];
      });
      return next;
    });
  }, [resourceIdsKey, debug]);

  useEffect(() => {
    const ids = getIdsFromKey(resourceIdsKey);
    if (!bucketName || ids.length === 0) {
      setItems({});
      setLoading(metaLoading);
      setError(metaError as Error | undefined);
      return;
    }

    const controller = new AbortController();
    const { signal } = controller;

    async function fetchJson(objectName: string): Promise<unknown> {
      // Try public media URL first (fast path if CORS is configured), fall back to local proxy
      const mediaUrl = `https://storage.googleapis.com/${bucketName}/${encodeURI(objectName)}`;
      try {
        if (debug) console.debug('[useResourcesMerged2] media fetch', { url: mediaUrl });
        const res = await fetch(mediaUrl, { signal, headers: { Accept: 'application/json' } });
        if (!res.ok) {
          const text = await res.text().catch(() => '');
          if (debug) console.error('[useResourcesMerged2] media fetch error', { status: res.status, statusText: res.statusText, text: text?.slice(0, 200) });
          throw new Error(`Media fetch failed ${res.status} ${res.statusText} for ${objectName}`);
        }
        return (await res.json()) as unknown;
      } catch (e) {
  const params = new URLSearchParams({ bucket: bucketName, object: objectName });
  const proxyUrl = `/api/gcs/fetch?${params.toString()}`;
        if (debug) console.debug('[useResourcesMerged2] proxy fallback', { url: proxyUrl, error: (e as Error)?.message });
        const res2 = await fetch(proxyUrl, { signal, headers: { Accept: 'application/json' } });
        if (!res2.ok) {
          const text2 = await res2.text().catch(() => '');
          if (debug) console.error('[useResourcesMerged2] proxy fetch error', { status: res2.status, statusText: res2.statusText, text: text2?.slice(0, 200) });
          throw new Error(`Proxy fetch failed ${res2.status} ${res2.statusText} for ${objectName}`);
        }
        return (await res2.json()) as unknown;
      }
    }

    const run = async () => {
      try {
        setLoading(true);
        const idsLocal = getIdsFromKey(resourceIdsKey);
        if (debug) console.debug('[useResourcesMerged2] fetch run start', { ids: idsLocal, bucketName });
        setError(undefined);

        const ids = idsLocal;
        const resultMap: Record<string, ResourceMerged[]> = {};
        let hadAnyError = false;
        for (const id of ids) {
          const metas = metaByType[id];
          if (!metas || metas.length === 0) {
            if (debug) console.debug('[useResourcesMerged2] no metas for id', { id });
            resultMap[id] = [];
            continue;
          }
          // Dedupe by path within a resource type to avoid redundant fetches
          const seen = new Set<string>();
          const metasDedup = metas.filter((m) => {
            if (seen.has(m.path)) return false;
            seen.add(m.path);
            return true;
          });
          if (debug) console.debug('[useResourcesMerged2] fetching metas', { id, count: metasDedup.length, sample: metasDedup.slice(0, 3).map(m => m.path) });
          const mergedOrNull = await Promise.all(
            metasDedup.map(async (m) => {
              try {
                const exposed = await fetchJson(m.path);
                return { ...m, exposedData: exposed } as unknown as ResourceMerged;
              } catch (e) {
                hadAnyError = true;
                if (debug) console.error('[useResourcesMerged2] fetch object error', { id, path: m.path, error: (e as Error)?.message });
                return null;
              }
            })
          );
          const merged = mergedOrNull.filter(Boolean) as ResourceMerged[];
          resultMap[id] = merged;
        }

        if (!signal.aborted) {
          if (debug) console.debug('[useResourcesMerged2] fetch complete', { ids, sizes: Object.fromEntries(Object.entries(resultMap).map(([k, v]) => [k, v.length])) });
          setItems(resultMap);
          if (hadAnyError) setError(new Error('One or more objects failed to fetch'));
        }
      } catch (e) {
        if (signal.aborted) return;
        if (debug) console.error('[useResourcesMerged2] fetch error', { error: (e as Error)?.message });
        setError(e as Error);
      } finally {
        if (!signal.aborted) {
          setLoading(false);
          if (debug) console.debug('[useResourcesMerged2] fetch run end');
        }
      }
    };

    run();
    return () => controller.abort();
    // Only re-run when bucket changes or meta content across all ids changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bucketName, metaDocsKey, resourceIdsKey, metaByType, metaLoading, debug]);

  if (debug) console.debug('[useResourcesMerged2] return', { loading: metaLoading || loading, hasError: !!((metaError as Error | undefined) ?? error), keys: Object.keys(items) });
  return { items, loading: metaLoading || loading, error: (metaError as Error | undefined) ?? error } as {
    items: Record<string, ResourceMerged[]>;
    loading: boolean;
    error: Error | undefined;
  };
};