'use server';

import type { PrimitiveDataJson } from '@toolproof/_schemas';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { PrimitiveConst, StepConst, WorkflowConst } from '@toolproof/_lib/types';
import { createCAFS } from 'gcs_utils';
// import { dbAdmin } from '@toolproof/_lib/firebaseAdminInit'; ATTENTION: import from here instead
import { getNewId as getNewIdLib } from '@toolproof/_lib';
import { dbAdmin } from '@/_lib/setup/firebaseAdminInit';
import { Storage } from '@google-cloud/storage';
import path from 'path';
import { existsSync } from 'fs';


// Ensure Application Default Credentials are resolvable for libraries that rely on ADC
// (e.g., gcs-utils via google-auth-library). Prefer an explicit env var, else fallback
// to the local gcp-key.json if present.
function ensureGcpADC() {
  if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    const localKeyPath = path.join(process.cwd(), 'gcp-key.json');
    if (existsSync(localKeyPath)) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = localKeyPath;
    }
  }
}

// Initialize once at module load so downstream libs can discover credentials
ensureGcpADC();

function getStorage(): Storage {
  const envKey = process.env.GOOGLE_APPLICATION_CREDENTIALS;
  if (envKey && existsSync(envKey)) {
    return new Storage({ keyFilename: envKey });
  }
  const localKeyPath = path.join(process.cwd(), 'gcp-key.json');
  if (existsSync(localKeyPath)) {
    return new Storage({ keyFilename: localKeyPath });
  }
  return new Storage();
}

export async function getNewId(identifiable: PrimitiveConst | StepConst | WorkflowConst | 'JOBSTEP') { // ATTENTION
  return getNewIdLib(identifiable);
}

function mimeToFilename(mime: string) {
  return encodeURIComponent(mime);
}

function filenameToMime(name: string) {
  return decodeURIComponent(name);
}

export async function uploadPrimitive(
  primitiveConst: PrimitiveConst,
  payload: PrimitiveDataJson,
  metaExcluded: string[], // DOC: List of keys to exclude primitive metadata saved to Firestore (besides 'id' which is always excluded as it is the document id)
  metaIncluded: Record<string, unknown> // DOC: Keys not in payload to include in primitive metadata saved to Firestore
) {

  const metaExcludedWithId = [...metaExcluded, 'id'];

  const docData: Record<string, unknown> = {};

  if (primitiveConst !== CONSTANTS.PRIMITIVES.formats && primitiveConst !== CONSTANTS.PRIMITIVES.signatures) {
    const filename = payload.name;
    const destination = `${primitiveConst}/${filename}.json`;

    const storage = getStorage();
    const file = storage.bucket(CONSTANTS.STORAGE.tp_primitives).file(destination);
    const content = JSON.stringify(payload, null, 2) + '\n';
    await file.save(content, {
      contentType: 'application/json; charset=utf-8',
      resumable: false,
      validation: 'md5',
      metadata: { cacheControl: 'no-cache' },
    });

    docData.path = destination;

  }

  const col = dbAdmin.collection(CONSTANTS.STORAGE.primitives).doc(primitiveConst).collection(CONSTANTS.STORAGE.members);
  const docRef = col.doc(payload.id);

  // Include all keys from the payload except those listed in `metaExcludedSet`.
  const metaExcludedSet = new Set(metaExcludedWithId || []);
  for (const key of Object.keys(payload)) {
    if (!key || metaExcludedSet.has(key)) continue;
    if (Object.prototype.hasOwnProperty.call(payload, key)) {
      // Type-guard: payload is Concept & Record<string, unknown>
      docData[key] = (payload as Record<string, unknown>)[key];
    }
  }

  // Include additional keys explicitly provided via `metaIncluded`
  if (metaIncluded && typeof metaIncluded === 'object') {
    for (const [key, value] of Object.entries(metaIncluded)) {
      if (!key || metaExcludedSet.has(key)) continue;
      docData[key] = value;
    }
  }

  await docRef.set(docData);
  return { ok: true, docId: docRef.id };
}

// ATTENTION
/** Writes an integer value to a Google Cloud Storage file, via CAFS
 * @param value The integer value to store
 */
export async function writeToCAFS(typeId: string, resourceId: string, data: string) {
  const cafs = createCAFS();
  try {
    const result = await cafs.storeContent(typeId, resourceId, data);
    return result;
  } catch (error) {
    throw new Error(`Failed to write file: ${error}`);
  }
}