{"name": "@toolproof/_schemas", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc -b", "build:scripts": "tsc -p tsconfig.scripts.json", "extractSchemas": "node ./dist/scripts/extractSchemas.js", "dereferenceAndInline": "node ./dist/scripts/dereferenceAndInline.js", "inlineImplWithDefs": "node ./dist/scripts/inlineImplementationDataWithDefs.js", "extractImplConst": "node ./dist/scripts/extractImplementationDataConst.js", "generateTypes": "node ./dist/scripts/generateTypes.js", "update": "rmdir /s /q dist && pnpm run build:scripts && pnpm run extractSchemas -- --in src/primitives/Primitives.json --out src/schemas/Primitives.json --id 'https://schemas.toolproof.com/v0/Primitives.json' && pnpm run inlineImplWithDefs && pnpm run extractImplConst && pnpm run generateTypes && pnpm run build && cls"}, "files": ["dist"], "devDependencies": {"@apidevtools/json-schema-ref-parser": "^14.2.1", "@types/node": "^20.8.1", "ajv-cli": "^5.0.0", "ajv-formats": "^3.0.1", "json-schema-to-typescript": "^15.0.4"}}