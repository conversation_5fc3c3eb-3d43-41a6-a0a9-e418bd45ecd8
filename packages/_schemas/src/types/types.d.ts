// Auto-generated from JSON schemas. Do not edit.

/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "BranchStep".
 */
export type BranchStep = Identifiable & {
  /**
   * @minItems 1
   */
  cases?: [ConditionalWrapper, ...ConditionalWrapper[]];
  id: BranchStepId;
  kind: "branch";
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "WorkStep".
 */
export type WorkStep = Identifiable & {
  execution: Execution;
  id: WorkStepId;
  kind: "work";
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Execution".
 */
export type Execution = Identifiable & {
  id: ExecutionId;
  implementationId: ImplementationId;
  resourceBindingMap: {
    inputBindingMap: ResourceBindingMap;
    outputBindingMap: ResourceBindingMap1;
    [k: string]: unknown;
  };
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ExecutionId".
 */
export type ExecutionId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ImplementationId".
 */
export type ImplementationId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ResourceId".
 */
export type ResourceId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "WorkStepId".
 */
export type WorkStepId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "BranchStepId".
 */
export type BranchStepId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Documented".
 */
export type Documented = Name & Description;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ForStep".
 */
export type ForStep = Identifiable & {
  case: ConditionalWrapper;
  id: ForStepId;
  kind: "for";
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ForStepId".
 */
export type ForStepId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "FormatBase".
 */
export type FormatBase = IdentifiableDocumented & {
  id: FormatId;
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "IdentifiableDocumented".
 */
export type IdentifiableDocumented = Identifiable & Documented;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "FormatId".
 */
export type FormatId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "FormatData".
 */
export type FormatData = FormatBase;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "FormatMeta".
 */
export type FormatMeta = FormatBase & Path;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ImplementationBase".
 */
export type ImplementationBase = IdentifiableDocumented & {
  id: ImplementationId;
  roleMap: {
    inputMap: ImplementationRoleMap;
    outputMap: ImplementationRoleMap1;
    [k: string]: unknown;
  };
  signatureId: SignatureId;
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "RoleLiteral".
 */
export type RoleLiteral = {
  typeId: TypeId;
  [k: string]: unknown;
} & Documented;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "TypeId".
 */
export type TypeId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "SignatureId".
 */
export type SignatureId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ImplementationData".
 */
export type ImplementationData = ImplementationBase1 & ImplementationDetails;
export type ImplementationBase1 = IdentifiableDocumented & {
  id: ImplementationId;
  roleMap: {
    inputMap: ImplementationRoleMap;
    outputMap: ImplementationRoleMap1;
    [k: string]: unknown;
  };
  signatureId: SignatureId;
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ImplementationMeta".
 */
export type ImplementationMeta = ImplementationBase & Path & Uri;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "JsonValue".
 */
export type JsonValue =
  | null
  | boolean
  | number
  | string
  | JsonValue[]
  | {
      [k: string]: JsonValue;
    };
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ResourceMetaActive".
 */
export type ResourceMetaActive = Identifiable & {
  id: ResourceId;
  [k: string]: unknown;
} & Timestamp & {
    exposedData:
      | null
      | boolean
      | number
      | string
      | JsonValue[]
      | {
          [k: string]: JsonValue;
        };
    [k: string]: unknown;
  } & (
    | Path
    | {
        pointer: ResourceId;
        [k: string]: unknown;
      }
  );
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ResourceMetaPassive".
 */
export type ResourceMetaPassive = Identifiable & {
  id: ResourceId;
  [k: string]: unknown;
} & Timestamp &
  Path;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "RoleId".
 */
export type RoleId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "SemanticIdentityProp".
 */
export type SemanticIdentityProp =
  | {
      type: "string" | "integer" | "boolean";
      [k: string]: unknown;
    }
  | {
      /**
       * @minItems 1
       */
      enum: [string, ...string[]];
      [k: string]: unknown;
    };
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "SemanticMeritProp".
 */
export type SemanticMeritProp =
  | {
      type: "number" | "integer";
      [k: string]: unknown;
    }
  | {
      /**
       * @minItems 1
       */
      enum: [number, ...number[]];
      [k: string]: unknown;
    };
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "SignatureBase".
 */
export type SignatureBase = Identifiable & {
  formatId: "FORMAT-ApplicationJob";
  id: SignatureId;
  roleMap: {
    inputMap: SignatureRoleMap;
    outputMap: SignatureRoleMap;
    [k: string]: unknown;
  };
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "SignatureData".
 */
export type SignatureData = SignatureBase & {
  exposedSchema?: ExposedSchema & {
    $schema: "https://json-schema.org/draft/2020-12/schema";
    type: "object";
    allOf: [
      {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        allOf: [
          {
            $schema: "https://json-schema.org/draft/2020-12/schema";
            type: "object";
            allOf: [
              {
                type: "object";
                required: ["id"];
                $anchor: "Identifiable";
                additionalProperties: true;
                $id: "Identifiable";
              },
              {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "object";
                allOf: [
                  {
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    properties: {
                      name: {
                        type: "string";
                        $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                        minLength: 1;
                        pattern: "^[A-Z].*";
                        semanticValidation: "Ajv custom keyword to verify name.";
                      };
                    };
                    required: ["name"];
                    $anchor: "Name";
                    additionalProperties: true;
                    $id: "Name";
                  },
                  {
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    properties: {
                      description: {
                        type: "string";
                        $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                        minLength: 1;
                        semanticValidation: "Ajv custom keyword to verify description.";
                      };
                    };
                    required: ["description"];
                    $anchor: "Description";
                    additionalProperties: true;
                    $id: "Description";
                  }
                ];
                $anchor: "Documented";
                required: [];
                additionalProperties: true;
                $id: "Documented";
              }
            ];
            $anchor: "IdentifiableDocumented";
            required: [];
            additionalProperties: true;
            $id: "IdentifiableDocumented";
          },
          {
            type: "object";
            properties: {
              id: {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "string";
                $anchor: "ImplementationId";
                pattern: "^IMPLEMENTATION-.+$";
              };
              roleMap: {
                type: "object";
                properties: {
                  inputMap: {
                    $comment: "This will be overlayed at runtime to specify an inputMap corresponding to the inputMap of the underlying signature.";
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    additionalProperties: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "object";
                      allOf: [
                        {
                          properties: {
                            typeId: {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "string";
                              $anchor: "TypeId";
                              pattern: "^TYPE-.+$";
                            };
                          };
                          required: ["typeId"];
                          additionalProperties: true;
                        },
                        {
                          $schema: "https://json-schema.org/draft/2020-12/schema";
                          type: "object";
                          allOf: [
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                name: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                                  minLength: 1;
                                  pattern: "^[A-Z].*";
                                  semanticValidation: "Ajv custom keyword to verify name.";
                                };
                              };
                              required: ["name"];
                              $anchor: "Name";
                              additionalProperties: true;
                              $id: "Name";
                            },
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                description: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                                  minLength: 1;
                                  semanticValidation: "Ajv custom keyword to verify description.";
                                };
                              };
                              required: ["description"];
                              $anchor: "Description";
                              additionalProperties: true;
                              $id: "Description";
                            }
                          ];
                          $anchor: "Documented";
                          required: [];
                          additionalProperties: true;
                          $id: "Documented";
                        }
                      ];
                      $anchor: "RoleLiteral";
                      required: [];
                      additionalProperties: true;
                      $id: "RoleLiteral";
                    };
                    $anchor: "ImplementationRoleMap";
                    propertyNames: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "string";
                      $anchor: "RoleId";
                      pattern: "^ROLE-.+$";
                    };
                    required: [];
                    $id: "ImplementationRoleMap";
                  };
                  outputMap: {
                    $comment: "This will be overlayed at runtime to specify an outputMap corresponding to the outputMap of the underlying signature.";
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    additionalProperties: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "object";
                      allOf: [
                        {
                          properties: {
                            typeId: {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "string";
                              $anchor: "TypeId";
                              pattern: "^TYPE-.+$";
                            };
                          };
                          required: ["typeId"];
                          additionalProperties: true;
                        },
                        {
                          $schema: "https://json-schema.org/draft/2020-12/schema";
                          type: "object";
                          allOf: [
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                name: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                                  minLength: 1;
                                  pattern: "^[A-Z].*";
                                  semanticValidation: "Ajv custom keyword to verify name.";
                                };
                              };
                              required: ["name"];
                              $anchor: "Name";
                              additionalProperties: true;
                              $id: "Name";
                            },
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                description: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                                  minLength: 1;
                                  semanticValidation: "Ajv custom keyword to verify description.";
                                };
                              };
                              required: ["description"];
                              $anchor: "Description";
                              additionalProperties: true;
                              $id: "Description";
                            }
                          ];
                          $anchor: "Documented";
                          required: [];
                          additionalProperties: true;
                          $id: "Documented";
                        }
                      ];
                      $anchor: "RoleLiteral";
                      required: [];
                      additionalProperties: true;
                      $id: "RoleLiteral";
                    };
                    $anchor: "ImplementationRoleMap";
                    propertyNames: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "string";
                      $anchor: "RoleId";
                      pattern: "^ROLE-.+$";
                    };
                    required: [];
                    $id: "ImplementationRoleMap";
                  };
                };
                required: ["inputMap", "outputMap"];
                additionalProperties: true;
              };
              signatureId: {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "string";
                $anchor: "SignatureId";
                pattern: "^SIGNATURE-.+$";
              };
            };
            required: ["id", "signatureId", "roleMap"];
            additionalProperties: true;
          }
        ];
        $anchor: "ImplementationBase";
        $comment: "This is stable across versions of the implementation.";
      },
      {
        $comment: "This is variable across versions of the implementation.";
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        properties: {
          envelopedSemantics: {
            type: "object";
            properties: {
              semanticIdentity: {
                type: "string";
                $comment: "This will be the commit hash of the implementation's repository at the time of creating or updating the implementation.";
                pattern: "^[0-9a-f]{7,40}$";
              };
            };
            required: ["semanticIdentity"];
            unevaluatedProperties: false;
            additionalProperties: true;
          };
        };
        required: ["envelopedSemantics"];
        $anchor: "ImplementationDetails";
      }
    ];
    $anchor: "ImplementationData";
    $defs: {
      ImplementationBase: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        allOf: [
          {
            $schema: "https://json-schema.org/draft/2020-12/schema";
            type: "object";
            allOf: [
              {
                type: "object";
                required: ["id"];
                $anchor: "Identifiable";
                additionalProperties: true;
                $id: "Identifiable";
              },
              {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "object";
                allOf: [
                  {
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    properties: {
                      name: {
                        type: "string";
                        $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                        minLength: 1;
                        pattern: "^[A-Z].*";
                        semanticValidation: "Ajv custom keyword to verify name.";
                      };
                    };
                    required: ["name"];
                    $anchor: "Name";
                    additionalProperties: true;
                    $id: "Name";
                  },
                  {
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    properties: {
                      description: {
                        type: "string";
                        $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                        minLength: 1;
                        semanticValidation: "Ajv custom keyword to verify description.";
                      };
                    };
                    required: ["description"];
                    $anchor: "Description";
                    additionalProperties: true;
                    $id: "Description";
                  }
                ];
                $anchor: "Documented";
                required: [];
                additionalProperties: true;
                $id: "Documented";
              }
            ];
            $anchor: "IdentifiableDocumented";
            required: [];
            additionalProperties: true;
            $id: "IdentifiableDocumented";
          },
          {
            type: "object";
            properties: {
              id: {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "string";
                $anchor: "ImplementationId";
                pattern: "^IMPLEMENTATION-.+$";
              };
              roleMap: {
                type: "object";
                properties: {
                  inputMap: {
                    $comment: "This will be overlayed at runtime to specify an inputMap corresponding to the inputMap of the underlying signature.";
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    additionalProperties: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "object";
                      allOf: [
                        {
                          properties: {
                            typeId: {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "string";
                              $anchor: "TypeId";
                              pattern: "^TYPE-.+$";
                            };
                          };
                          required: ["typeId"];
                          additionalProperties: true;
                        },
                        {
                          $schema: "https://json-schema.org/draft/2020-12/schema";
                          type: "object";
                          allOf: [
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                name: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                                  minLength: 1;
                                  pattern: "^[A-Z].*";
                                  semanticValidation: "Ajv custom keyword to verify name.";
                                };
                              };
                              required: ["name"];
                              $anchor: "Name";
                              additionalProperties: true;
                              $id: "Name";
                            },
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                description: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                                  minLength: 1;
                                  semanticValidation: "Ajv custom keyword to verify description.";
                                };
                              };
                              required: ["description"];
                              $anchor: "Description";
                              additionalProperties: true;
                              $id: "Description";
                            }
                          ];
                          $anchor: "Documented";
                          required: [];
                          additionalProperties: true;
                          $id: "Documented";
                        }
                      ];
                      $anchor: "RoleLiteral";
                      required: [];
                      additionalProperties: true;
                      $id: "RoleLiteral";
                    };
                    $anchor: "ImplementationRoleMap";
                    propertyNames: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "string";
                      $anchor: "RoleId";
                      pattern: "^ROLE-.+$";
                    };
                  };
                  outputMap: {
                    $comment: "This will be overlayed at runtime to specify an outputMap corresponding to the outputMap of the underlying signature.";
                    $schema: "https://json-schema.org/draft/2020-12/schema";
                    type: "object";
                    additionalProperties: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "object";
                      allOf: [
                        {
                          properties: {
                            typeId: {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "string";
                              $anchor: "TypeId";
                              pattern: "^TYPE-.+$";
                            };
                          };
                          required: ["typeId"];
                          additionalProperties: true;
                        },
                        {
                          $schema: "https://json-schema.org/draft/2020-12/schema";
                          type: "object";
                          allOf: [
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                name: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                                  minLength: 1;
                                  pattern: "^[A-Z].*";
                                  semanticValidation: "Ajv custom keyword to verify name.";
                                };
                              };
                              required: ["name"];
                              $anchor: "Name";
                              additionalProperties: true;
                              $id: "Name";
                            },
                            {
                              $schema: "https://json-schema.org/draft/2020-12/schema";
                              type: "object";
                              properties: {
                                description: {
                                  type: "string";
                                  $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                                  minLength: 1;
                                  semanticValidation: "Ajv custom keyword to verify description.";
                                };
                              };
                              required: ["description"];
                              $anchor: "Description";
                              additionalProperties: true;
                              $id: "Description";
                            }
                          ];
                          $anchor: "Documented";
                          required: [];
                          additionalProperties: true;
                          $id: "Documented";
                        }
                      ];
                      $anchor: "RoleLiteral";
                      required: [];
                      additionalProperties: true;
                      $id: "RoleLiteral";
                    };
                    $anchor: "ImplementationRoleMap";
                    propertyNames: {
                      $schema: "https://json-schema.org/draft/2020-12/schema";
                      type: "string";
                      $anchor: "RoleId";
                      pattern: "^ROLE-.+$";
                    };
                  };
                };
                required: ["inputMap", "outputMap"];
              };
              signatureId: {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "string";
                $anchor: "SignatureId";
                pattern: "^SIGNATURE-.+$";
              };
            };
            required: ["id", "signatureId", "roleMap"];
          }
        ];
        $anchor: "ImplementationBase";
      };
      ImplementationDetails: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        properties: {
          envelopedSemantics: {
            type: "object";
            properties: {
              semanticIdentity: {
                type: "string";
                $comment: "This will be the commit hash of the implementation's repository at the time of creating or updating the implementation.";
                pattern: "^[0-9a-f]{7,40}$";
              };
            };
            required: ["semanticIdentity"];
            unevaluatedProperties: false;
          };
        };
        required: ["envelopedSemantics"];
        $anchor: "ImplementationDetails";
      };
      IdentifiableDocumented: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        allOf: [
          {type: "object"; required: ["id"]; $anchor: "Identifiable"; additionalProperties: true; $id: "Identifiable"},
          {
            $schema: "https://json-schema.org/draft/2020-12/schema";
            type: "object";
            allOf: [
              {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "object";
                properties: {
                  name: {
                    type: "string";
                    $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                    minLength: 1;
                    pattern: "^[A-Z].*";
                    semanticValidation: "Ajv custom keyword to verify name.";
                  };
                };
                required: ["name"];
                $anchor: "Name";
                additionalProperties: true;
                $id: "Name";
              },
              {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "object";
                properties: {
                  description: {
                    type: "string";
                    $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                    minLength: 1;
                    semanticValidation: "Ajv custom keyword to verify description.";
                  };
                };
                required: ["description"];
                $anchor: "Description";
                additionalProperties: true;
                $id: "Description";
              }
            ];
            $anchor: "Documented";
            required: [];
            additionalProperties: true;
            $id: "Documented";
          }
        ];
        $anchor: "IdentifiableDocumented";
      };
      ImplementationId: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "string";
        $anchor: "ImplementationId";
        pattern: "^IMPLEMENTATION-.+$";
      };
      ImplementationRoleMap: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        additionalProperties: {
          $schema: "https://json-schema.org/draft/2020-12/schema";
          type: "object";
          allOf: [
            {
              properties: {
                typeId: {
                  $schema: "https://json-schema.org/draft/2020-12/schema";
                  type: "string";
                  $anchor: "TypeId";
                  pattern: "^TYPE-.+$";
                };
              };
              required: ["typeId"];
              additionalProperties: true;
            },
            {
              $schema: "https://json-schema.org/draft/2020-12/schema";
              type: "object";
              allOf: [
                {
                  $schema: "https://json-schema.org/draft/2020-12/schema";
                  type: "object";
                  properties: {
                    name: {
                      type: "string";
                      $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                      minLength: 1;
                      pattern: "^[A-Z].*";
                      semanticValidation: "Ajv custom keyword to verify name.";
                    };
                  };
                  required: ["name"];
                  $anchor: "Name";
                  additionalProperties: true;
                  $id: "Name";
                },
                {
                  $schema: "https://json-schema.org/draft/2020-12/schema";
                  type: "object";
                  properties: {
                    description: {
                      type: "string";
                      $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                      minLength: 1;
                      semanticValidation: "Ajv custom keyword to verify description.";
                    };
                  };
                  required: ["description"];
                  $anchor: "Description";
                  additionalProperties: true;
                  $id: "Description";
                }
              ];
              $anchor: "Documented";
              required: [];
              additionalProperties: true;
              $id: "Documented";
            }
          ];
          $anchor: "RoleLiteral";
          required: [];
          additionalProperties: true;
          $id: "RoleLiteral";
        };
        $anchor: "ImplementationRoleMap";
        propertyNames: {
          $schema: "https://json-schema.org/draft/2020-12/schema";
          type: "string";
          $anchor: "RoleId";
          pattern: "^ROLE-.+$";
        };
      };
      SignatureId: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "string";
        $anchor: "SignatureId";
        pattern: "^SIGNATURE-.+$";
      };
      Identifiable: {type: "object"; required: ["id"]; $anchor: "Identifiable"};
      Documented: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        allOf: [
          {
            $schema: "https://json-schema.org/draft/2020-12/schema";
            type: "object";
            properties: {
              name: {
                type: "string";
                $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                minLength: 1;
                pattern: "^[A-Z].*";
                semanticValidation: "Ajv custom keyword to verify name.";
              };
            };
            required: ["name"];
            $anchor: "Name";
            additionalProperties: true;
            $id: "Name";
          },
          {
            $schema: "https://json-schema.org/draft/2020-12/schema";
            type: "object";
            properties: {
              description: {
                type: "string";
                $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                minLength: 1;
                semanticValidation: "Ajv custom keyword to verify description.";
              };
            };
            required: ["description"];
            $anchor: "Description";
            additionalProperties: true;
            $id: "Description";
          }
        ];
        $anchor: "Documented";
      };
      RoleLiteral: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        allOf: [
          {
            properties: {
              typeId: {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "string";
                $anchor: "TypeId";
                pattern: "^TYPE-.+$";
              };
            };
            required: ["typeId"];
          },
          {
            $schema: "https://json-schema.org/draft/2020-12/schema";
            type: "object";
            allOf: [
              {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "object";
                properties: {
                  name: {
                    type: "string";
                    $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
                    minLength: 1;
                    pattern: "^[A-Z].*";
                    semanticValidation: "Ajv custom keyword to verify name.";
                  };
                };
                required: ["name"];
                $anchor: "Name";
                additionalProperties: true;
                $id: "Name";
              },
              {
                $schema: "https://json-schema.org/draft/2020-12/schema";
                type: "object";
                properties: {
                  description: {
                    type: "string";
                    $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
                    minLength: 1;
                    semanticValidation: "Ajv custom keyword to verify description.";
                  };
                };
                required: ["description"];
                $anchor: "Description";
                additionalProperties: true;
                $id: "Description";
              }
            ];
            $anchor: "Documented";
            required: [];
            additionalProperties: true;
            $id: "Documented";
          }
        ];
        $anchor: "RoleLiteral";
      };
      RoleId: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "string";
        $anchor: "RoleId";
        pattern: "^ROLE-.+$";
      };
      Name: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        properties: {
          name: {
            type: "string";
            $comment: "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.";
            minLength: 1;
            pattern: "^[A-Z].*";
            semanticValidation: "Ajv custom keyword to verify name.";
          };
        };
        required: ["name"];
        $anchor: "Name";
      };
      Description: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "object";
        properties: {
          description: {
            type: "string";
            $comment: "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.";
            minLength: 1;
            semanticValidation: "Ajv custom keyword to verify description.";
          };
        };
        required: ["description"];
        $anchor: "Description";
      };
      TypeId: {
        $schema: "https://json-schema.org/draft/2020-12/schema";
        type: "string";
        $anchor: "TypeId";
        pattern: "^TYPE-.+$";
      };
    };
  };
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ExposedSchema".
 */
export type ExposedSchema = unknown &
  (
    | {
        allOf: unknown[];
        [k: string]: unknown;
      }
    | {
        [k: string]: unknown;
      }
  ) &
  (
    | {
        additionalProperties: false;
        [k: string]: unknown;
      }
    | {
        unevaluatedProperties: false;
        [k: string]: unknown;
      }
  ) & {
    $id?: string;
    $schema: "https://json-schema.org/draft/2020-12/schema";
    $defs?: {
      [k: string]: unknown;
    };
    type: "object";
    allOf?: {
      [k: string]: unknown;
    }[];
    properties?: {
      semanticIdentity?: SemanticIdentityProp;
      semanticMerit?: SemanticMeritProp;
      [k: string]: unknown;
    };
    /**
     * @minItems 1
     */
    required?: [string, ...string[]];
    $anchor?: string;
    envelopedSemantics?: {
      semanticIdentity?: SemanticIdentityProp;
      semanticMerit?: SemanticMeritProp;
      [k: string]: unknown;
    };
    [k: string]: unknown;
  } & {
    $id?: string;
    $schema: "https://json-schema.org/draft/2020-12/schema";
    $defs?: {
      [k: string]: unknown;
    };
    type: "object";
    allOf?: {
      [k: string]: unknown;
    }[];
    properties?: {
      semanticIdentity?: SemanticIdentityProp;
      semanticMerit?: SemanticMeritProp;
      [k: string]: unknown;
    };
    /**
     * @minItems 1
     */
    required?: [string, ...string[]];
    $anchor?: string;
    envelopedSemantics?: {
      semanticIdentity?: SemanticIdentityProp;
      semanticMerit?: SemanticMeritProp;
      [k: string]: unknown;
    };
    [k: string]: unknown;
  } & {
    $id?: string;
    $schema: "https://json-schema.org/draft/2020-12/schema";
    $defs?: {
      [k: string]: unknown;
    };
    type: "object";
    allOf?: {
      [k: string]: unknown;
    }[];
    properties?: {
      semanticIdentity?: SemanticIdentityProp;
      semanticMerit?: SemanticMeritProp;
      [k: string]: unknown;
    };
    /**
     * @minItems 1
     */
    required?: [string, ...string[]];
    $anchor?: string;
    envelopedSemantics?: {
      semanticIdentity?: SemanticIdentityProp;
      semanticMerit?: SemanticMeritProp;
      [k: string]: unknown;
    };
    [k: string]: unknown;
  };
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "SignatureMeta".
 */
export type SignatureMeta = SignatureBase & Path;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Step".
 */
export type Step = WorkStep | BranchStep | WhileStep | ForStep;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "WhileStep".
 */
export type WhileStep = Identifiable & {
  case: ConditionalWrapper;
  id: WhileStepId;
  kind: "while";
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "WhileStepId".
 */
export type WhileStepId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "StepId".
 */
export type StepId = WorkStepId | BranchStepId | WhileStepId | ForStepId;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "TypeBase".
 */
export type TypeBase = IdentifiableDocumented & {
  formatId: FormatId;
  id: TypeId;
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "TypeData".
 */
export type TypeData = TypeBase & {
  exposedSchema: ExposedSchema;
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "TypeMeta".
 */
export type TypeMeta = TypeBase &
  Path & {
    [k: string]: unknown;
  };
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Workflow".
 */
export type Workflow = Identifiable & {
  id: WorkflowId;
  steps: Step[];
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "WorkflowId".
 */
export type WorkflowId = string;
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "WorkflowSpec".
 */
export type WorkflowSpec = Identifiable & {
  id: WorkflowSpecId;
  resourceMaps: ResourceMap[];
  workflow: Workflow;
  [k: string]: unknown;
};
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "WorkflowSpecId".
 */
export type WorkflowSpecId = string;

export interface HttpsSchemasToolproofComV0PrimitivesJson {
  [k: string]: unknown;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Identifiable".
 */
export interface Identifiable {
  [k: string]: unknown;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ConditionalWrapper".
 */
export interface ConditionalWrapper {
  what: WorkStep;
  when: WorkStep;
  [k: string]: unknown;
}
export interface ResourceBindingMap {
  [k: string]: ResourceId;
}
export interface ResourceBindingMap1 {
  [k: string]: ResourceId;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Description".
 */
export interface Description {
  description: string;
  [k: string]: unknown;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Name".
 */
export interface Name {
  name: string;
  [k: string]: unknown;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Path".
 */
export interface Path {
  path: string;
  [k: string]: unknown;
}
export interface ImplementationRoleMap {
  [k: string]: RoleLiteral;
}
export interface ImplementationRoleMap1 {
  [k: string]: RoleLiteral;
}
export interface ImplementationDetails {
  envelopedSemantics: {
    semanticIdentity: string;
    [k: string]: unknown;
  };
  [k: string]: unknown;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ImplementationDetails".
 */
export interface ImplementationDetails1 {
  envelopedSemantics: {
    semanticIdentity: string;
    [k: string]: unknown;
  };
  [k: string]: unknown;
}
export interface Uri {
  uri: string;
  [k: string]: unknown;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ImplementationRoleMap".
 */
export interface ImplementationRoleMap2 {
  [k: string]: RoleLiteral;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ResourceBindingMap".
 */
export interface ResourceBindingMap2 {
  [k: string]: ResourceId;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "ResourceMap".
 */
export interface ResourceMap {
  [k: string]: ResourceMetaActive;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Timestamp".
 */
export interface Timestamp {
  timestamp: string;
  [k: string]: unknown;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "SignatureRoleMap".
 */
export interface SignatureRoleMap {
  [k: string]: TypeId;
}
/**
 * This interface was referenced by `HttpsSchemasToolproofComV0PrimitivesJson`'s JSON-Schema
 * via the `definition` "Uri".
 */
export interface Uri1 {
  uri: string;
  [k: string]: unknown;
}
