{"id": "TYPE-Primitives", "name": "Primitives", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$comment": "This schema defines all primitive schemas used throughout the Toolproof ecosystem. The primitive schemas themselves are defined in $defs.<Primitive>.exposedSchema. The build process (via extractSchemas.js) extracts these schemas and writes them to a separate file (src/schemas/Primitives.json). The reason for this indirection is to have all these schema envelopes validate positively against the schema at $defs/TypeData, effectively making them ResourceTypes, which are first-class citizens in the Toolproof ecosystem. Be aware that the terms ResourceFormat, ResourceType, and ResourceRole used in ToolProof's official documentation correspond to Format, Type, and Role here and throughout the codebase. Also, the terms JobSignature, JobImplementation, and JobExecution in the documentation correspond to Signature, Implementation, and Execution here and throughout the codebase. This is to avoid long names in the codebase.", "$id": "https://schemas.toolproof.com/v0/Primitives.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "$defs": {"Identifiable": {"id": "TYPE-Identifiable", "name": "Identifiable", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Identifiable", "type": "object", "required": ["id"]}}, "Name": {"id": "TYPE-Name", "name": "Name", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Name", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["name"], "properties": {"name": {"type": "string", "minLength": 1, "pattern": "^[A-Z].*", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "semanticValidation": "Ajv custom keyword to verify name."}}}}, "Description": {"id": "TYPE-Description", "name": "Description", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Description", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["description"], "properties": {"description": {"type": "string", "minLength": 1, "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "semanticValidation": "Ajv custom keyword to verify description."}}}}, "Documented": {"id": "TYPE-Documented", "name": "Documented", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Documented", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}]}}, "IdentifiableDocumented": {"id": "TYPE-IdentifiableDocumented", "name": "IdentifiableDocumented", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "IdentifiableDocumented", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/Documented"}]}}, "Path": {"id": "TYPE-Path", "name": "Path", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Path", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["path"], "properties": {"path": {"type": "string", "format": "uri"}}}}, "Uri": {"id": "TYPE-<PERSON>ri", "name": "<PERSON><PERSON>", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "<PERSON><PERSON>", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["uri"], "properties": {"uri": {"type": "string", "format": "uri"}}}}, "TypeId": {"id": "TYPE-TypeId", "name": "TypeId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "TypeId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^TYPE-.+$"}}, "FormatId": {"id": "TYPE-FormatId", "name": "FormatId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "FormatId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORMAT-.+$"}}, "FormatBase": {"id": "TYPE-FormatBase", "name": "FormatBase", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "FormatBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"required": ["id"], "properties": {"id": {"$ref": "#/$defs/FormatId"}}}]}}, "FormatData": {"id": "TYPE-FormatData", "name": "FormatData", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "FormatData", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}], "unevaluatedProperties": false}}, "FormatMeta": {"id": "TYPE-FormatMeta", "name": "FormatMeta", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "FormatMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false}}, "ExposedSchema": {"id": "TYPE-ExposedSchema", "name": "ExposedSchema", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ExposedSchema", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$schema", "type"], "properties": {"$id": {"type": "string", "format": "uri"}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "type": {"const": "object"}, "required": {"type": "array", "items": {"type": "string"}, "minItems": 1, "uniqueItems": true}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"semanticIdentity": {"$ref": "#/$defs/SemanticIdentityProp"}, "semanticMerit": {"$ref": "#/$defs/SemanticMeritProp"}}}, "envelopedSemantics": {"$comment": "This is to allow semantic properties to be nested under a stable property name instead of at the root level. This is used by ImplementationDetails but could be used by other types (also user-created types) in the future.", "type": "object", "properties": {"semanticIdentity": {"$ref": "#/$defs/SemanticIdentityProp"}, "semanticMerit": {"$ref": "#/$defs/SemanticMeritProp"}}}, "$defs": {"type": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}}, "allOf": [{"anyOf": [{"required": ["$id"]}, {"required": ["$anchor"]}, true]}, {"oneOf": [{"required": ["allOf"], "properties": {"allOf": {"type": "array", "uniqueItems": true, "contains": {"type": "object", "required": ["required", "properties"]}}}}, {"required": ["required", "properties"]}]}, {"anyOf": [{"required": ["additionalProperties"], "properties": {"additionalProperties": {"const": false}}}, {"required": ["unevaluatedProperties"], "properties": {"unevaluatedProperties": {"const": false}}}]}], "unevaluatedProperties": false}}, "SemanticIdentityProp": {"id": "TYPE-SemanticIdentityProp", "name": "SemanticIdentityProp", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "SemanticIdentityProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"required": ["type"], "properties": {"type": {"enum": ["string", "integer", "boolean"]}}}, {"required": ["enum"], "properties": {"enum": {"type": "array", "items": {"type": "string"}, "minItems": 1}}}]}}, "SemanticMeritProp": {"id": "TYPE-SemanticMeritProp", "name": "SemanticMeritProp", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "SemanticMeritProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"required": ["type"], "properties": {"type": {"enum": ["number", "integer"]}}}, {"required": ["enum"], "properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}}]}}, "TypeBase": {"id": "TYPE-TypeBase", "name": "TypeBase", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "TypeBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"required": ["id", "formatId"], "properties": {"id": {"$ref": "#/$defs/TypeId"}, "formatId": {"$ref": "#/$defs/FormatId"}}}]}}, "TypeData": {"id": "TYPE-TypeData", "name": "TypeData", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "TypeData", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"required": ["exposedSchema"], "properties": {"exposedSchema": {"$ref": "#/$defs/ExposedSchema"}}}]}}, "TypeMeta": {"id": "TYPE-TypeMeta", "name": "TypeMeta", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "TypeMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"$ref": "#/$defs/Path"}, {"if": {"$comment": "If formatId is FORMAT-ApplicationJson, then uri must not be present, but if formatId is not FORMAT-ApplicationJson, then uri must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor.", "properties": {"formatId": {"const": "FORMAT-ApplicationJson"}}}, "then": {"not": {"$ref": "#/$defs/Uri"}}, "else": {"$ref": "#/$defs/Uri"}}], "unevaluatedProperties": false}}, "RoleId": {"id": "TYPE-RoleId", "name": "RoleId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "RoleId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^ROLE-.+$"}}, "RoleLiteral": {"id": "TYPE-RoleLiteral", "name": "RoleLiteral", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "RoleLiteral", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["typeId"], "properties": {"typeId": {"$ref": "#/$defs/TypeId"}}}, {"$ref": "#/$defs/Documented"}]}}, "SignatureRoleMap": {"id": "TYPE-SignatureRoleMap", "name": "SignatureRoleMap", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "SignatureRoleMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/RoleId"}, "additionalProperties": {"$ref": "#/$defs/TypeId"}}}, "ImplementationRoleMap": {"id": "TYPE-ImplementationRoleMap", "name": "ImplementationRoleMap", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ImplementationRoleMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/RoleId"}, "additionalProperties": {"$ref": "#/$defs/RoleLiteral"}}}, "SignatureId": {"id": "TYPE-SignatureId", "name": "SignatureId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "SignatureId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^SIGNATURE-.+$"}}, "SignatureBase": {"id": "TYPE-{SignatureBase}", "name": "TYPE-{SignatureBase}", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "SignatureBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"required": ["id", "formatId", "roleMap"], "properties": {"id": {"$ref": "#/$defs/SignatureId"}, "formatId": {"$comment": "We have formatId here in order to make signatures type-like.", "const": "FORMAT-ApplicationJob"}, "roleMap": {"type": "object", "required": ["inputMap", "outputMap"], "properties": {"inputMap": {"$ref": "#/$defs/SignatureRoleMap"}, "outputMap": {"$ref": "#/$defs/SignatureRoleMap"}}}}}]}}, "SignatureData": {"id": "TYPE-{SignatureData}", "name": "TYPE-{SignatureData}", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "SignatureData", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/SignatureBase"}, {"$comment": "We're not requiring exposedSchema here for now", "properties": {"exposedSchema": {"$comment": "The const is generated in the build process (via extractImplementationDataConst.ts and inlineImplementationDataWithDefs.ts) and it equals the extracted subschema (with all its direct and indirect dependencies inlined as $defs) at #defs/ImplementationData. This means that exposedSchema must both satisfy #defs/ExposedSchema and be the exact schema (seen as a data instance!) defined at #defs/ImplementationData. It also means that ImplementationData must itself satisfy ExposedSchema.", "allOf": [{"$ref": "#/$defs/ExposedSchema"}, {}]}}}], "unevaluatedProperties": false}}, "SignatureMeta": {"id": "TYPE-{SignatureMeta}", "name": "TYPE-{SignatureMeta}", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "SignatureMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/SignatureBase"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false}}, "ImplementationDetails": {"id": "TYPE-ImplementationDetails", "name": "ImplementationDetails", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ImplementationDetails", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["envelopedSemantics"], "properties": {"envelopedSemantics": {"type": "object", "properties": {"semanticIdentity": {"type": "string", "pattern": "^[0-9a-f]{7,40}$", "$comment": "This will be the commit hash of the implementation's repository at the time of creating or updating the implementation."}}, "required": ["semanticIdentity"], "unevaluatedProperties": false}}}}, "ImplementationId": {"id": "TYPE-ImplementationId", "name": "ImplementationId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ImplementationId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^IMPLEMENTATION-.+$"}}, "ImplementationBase": {"id": "TYPE-ImplementationBase", "name": "ImplementationBase", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ImplementationBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"type": "object", "required": ["id", "signatureId", "roleMap"], "properties": {"id": {"$ref": "#/$defs/ImplementationId"}, "signatureId": {"$ref": "#/$defs/SignatureId"}, "roleMap": {"type": "object", "required": ["inputMap", "outputMap"], "properties": {"inputMap": {"$comment": "This will be overlayed at runtime to specify an inputMap corresponding to the inputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}, "outputMap": {"$comment": "This will be overlayed at runtime to specify an outputMap corresponding to the outputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}}}}}]}}, "ImplementationData": {"id": "TYPE-ImplementationData", "name": "ImplementationData", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ImplementationData", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This is stable across versions of the implementation.", "$ref": "#/$defs/ImplementationBase"}, {"$comment": "This is variable across versions of the implementation.", "$ref": "#/$defs/ImplementationDetails"}]}}, "ImplementationMeta": {"id": "TYPE-ImplementationMeta", "name": "ImplementationMeta", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ImplementationMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ImplementationBase"}, {"$ref": "#/$defs/Path"}, {"$comment": "This points to the actual code implementation in ToolProof's Kubernetes cluster.", "$ref": "#/$defs/Uri"}]}}, "ResourceId": {"id": "TYPE-ResourceId", "name": "ResourceId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ResourceId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$comment": "This should be generated in the build process to keep the definitions DRY. We should also consider having an explicit ResourceId definition (RESOURCE-...) and rather let the ResourceMetas (ResourceMetaActive and ResourceMetaPassive) compose roleId and executionId.", "pattern": "^ROLE-.+__EXECUTION-.+$"}}, "ResourceBindingMap": {"id": "TYPE-ResourceBindingMap", "name": "ResourceBindingMap", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ResourceBindingMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/RoleId"}, "additionalProperties": {"$ref": "#/$defs/ResourceId"}}}, "ExecutionId": {"id": "TYPE-ExecutionId", "name": "ExecutionId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ExecutionId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^EXECUTION-.+$"}}, "Execution": {"id": "TYPE-Execution", "name": "Execution", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Execution", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "implementationId", "resourceBindingMap"], "properties": {"id": {"$ref": "#/$defs/ExecutionId"}, "implementationId": {"$ref": "#/$defs/ImplementationId"}, "resourceBindingMap": {"type": "object", "required": ["inputBindingMap", "outputBindingMap"], "properties": {"inputBindingMap": {"$comment": "This will be overlayed at runtime to specify an inputBindingMap corresponding to the inputMap of the underlying implementation.", "$ref": "#/$defs/ResourceBindingMap"}, "outputBindingMap": {"$comment": "This will be overlayed at runtime to specify an outputBindingMap corresponding to the outMap of the underlying implementation.", "$ref": "#/$defs/ResourceBindingMap"}}, "unevaluatedProperties": false}}}]}}, "ConditionalWrapper": {"id": "TYPE-ConditionalWrapper", "name": "ConditionalWrapper", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ConditionalWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["when", "what"], "properties": {"when": {"$ref": "#/$defs/WorkStep"}, "what": {"$ref": "#/$defs/WorkStep"}}, "unevaluatedProperties": false}}, "WorkStepId": {"id": "TYPE-WorkStepId", "name": "WorkStepId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "WorkStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKSTEP-.+$"}}, "WorkStep": {"id": "TYPE-WorkStep", "name": "WorkStep", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "WorkStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "kind", "execution"], "properties": {"id": {"$ref": "#/$defs/WorkStepId"}, "kind": {"const": "work"}, "execution": {"$ref": "#/$defs/Execution"}}}]}}, "BranchStepId": {"id": "TYPE-BranchStepId", "name": "BranchStepId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "BranchStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^BRANCHSTEP-.+$"}}, "BranchStep": {"id": "TYPE-BranchStep", "name": "BranchStep", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "BranchStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "kind", "case"], "properties": {"id": {"$ref": "#/$defs/BranchStepId"}, "kind": {"const": "branch"}, "cases": {"type": "array", "items": {"$ref": "#/$defs/ConditionalWrapper"}, "minItems": 1, "uniqueItems": true}}}]}}, "WhileStepId": {"id": "TYPE-WhileStepId", "name": "WhileStepId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "WhileStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WHILESTEP-.+$"}}, "WhileStep": {"id": "TYPE-WhileStep", "name": "WhileStep", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "WhileStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "kind", "case"], "properties": {"id": {"$ref": "#/$defs/WhileStepId"}, "kind": {"const": "while"}, "case": {"$ref": "#/$defs/ConditionalWrapper"}}}]}}, "ForStepId": {"id": "TYPE-ForStepId", "name": "ForStepId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ForStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORSTEP-.+$"}}, "ForStep": {"id": "TYPE-ForStep", "name": "ForStep", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ForStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "kind", "case"], "properties": {"id": {"$ref": "#/$defs/ForStepId"}, "kind": {"const": "for"}, "case": {"$ref": "#/$defs/ConditionalWrapper"}}}]}}, "StepId": {"id": "TYPE-StepId", "name": "StepId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "StepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStepId"}, {"$ref": "#/$defs/BranchStepId"}, {"$ref": "#/$defs/WhileStepId"}, {"$ref": "#/$defs/ForStepId"}]}}, "Step": {"id": "TYPE-Step", "name": "Step", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Step", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false}}, "WorkflowId": {"id": "TYPE-WorkflowId", "name": "WorkflowId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "WorkflowId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKFLOW-.+$"}}, "Workflow": {"id": "TYPE-Workflow", "name": "Workflow", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Workflow", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "steps"], "properties": {"id": {"$ref": "#/$defs/WorkflowId"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}}], "unevaluatedProperties": false}}, "Timestamp": {"id": "TYPE-Timestamp", "name": "Timestamp", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "Timestamp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time", "default": "1970-01-01T00:00:00Z"}}}}, "JsonValue": {"id": "TYPE-JsonValue", "name": "JsonValue", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "JsonValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonValue"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonValue"}}]}}, "ResourceMetaActive": {"id": "TYPE-ResourceMetaActive", "name": "ResourceMetaActive", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ResourceMetaActive", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"required": ["id"], "properties": {"id": {"$ref": "#/$defs/ResourceId"}}}, {"$ref": "#/$defs/Timestamp"}, {"required": ["exposedData"], "properties": {"exposedData": {"$comment": "This is overlayed at runtime to specify the actual data of the resource.", "$ref": "#/$defs/JsonValue"}}}, {"oneOf": [{"$ref": "#/$defs/Path"}, {"required": ["pointer"], "properties": {"pointer": {"$ref": "#/$defs/ResourceId"}}}]}], "unevaluatedProperties": false}}, "ResourceMetaPassive": {"id": "TYPE-ResourceMetaPassive", "name": "ResourceMetaPassive", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ResourceMetaPassive", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"required": ["id"], "properties": {"id": {"$ref": "#/$defs/ResourceId"}}}, {"$ref": "#/$defs/Timestamp"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false}}, "ResourceMap": {"id": "TYPE-ResourceMap", "name": "ResourceMap", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "ResourceMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/ResourceId"}, "additionalProperties": {"$ref": "#/$defs/ResourceMetaActive"}}}, "WorkflowSpecId": {"id": "TYPE-WorkflowSpecId", "name": "WorkflowSpecId", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "WorkflowSpecId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKFLOWSPEC-.+$"}}, "WorkflowSpec": {"id": "TYPE-WorkflowSpec", "name": "WorkflowSpec", "formatId": "FORMAT-ApplicationJson", "exposedSchema": {"$anchor": "WorkflowSpec", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"required": ["id", "workflow", "resourceMaps"], "properties": {"id": {"$ref": "#/$defs/WorkflowSpecId"}, "workflow": {"$ref": "#/$defs/Workflow"}, "resourceMaps": {"type": "array", "items": {"$ref": "#/$defs/ResourceMap"}, "uniqueItems": true}}}]}}}}}