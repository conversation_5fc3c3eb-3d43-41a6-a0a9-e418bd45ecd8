
import fs from 'fs';
import path from 'path';
// Custom dereferencer: no external deps

/**
 * Reads Primitives.json, dereferences #/$defs/ImplementationData, and inlines it as a const in the second allOf slot
 * at the exposedSchema location in SignatureData.
 */
async function dereferenceAndInline() {
    const projectRoot = process.cwd();
    const inputDir = path.join(projectRoot, 'src', 'schemas');
    // Use inputDir for Primitives.json path
    const primitivesPath = path.join(inputDir, 'Primitives.json');
    const raw = fs.readFileSync(primitivesPath, 'utf-8');
    const json = JSON.parse(raw);

    // Get the root ImplementationData schema and custom-dereference it locally
    const implementationDataRoot = (json as any).$defs?.ImplementationData;
    if (!implementationDataRoot) throw new Error('ImplementationData not found in $defs');

    const implementationData = derefLocalRefs(implementationDataRoot, json);

    // Find SignatureData.exposedSchema.allOf and replace the second slot with the dereferenced schema as a const
    const sigData = json.$defs?.SignatureData;
    if (!sigData) throw new Error('SignatureData not found');
    const exposedSchema = sigData.allOf?.[1]?.properties?.exposedSchema;
    if (!exposedSchema || !Array.isArray(exposedSchema.allOf)) throw new Error('exposedSchema.allOf not found');

    // Inline the dereferenced ImplementationData as a const in the second allOf slot
    exposedSchema.allOf[1] = { "const": implementationData };

    // Write back to file
    fs.writeFileSync(primitivesPath, JSON.stringify(json, null, 2));
    console.log('Primitives.json updated with inlined ImplementationData.');
}


// Run as script (ESM: always executes when called directly)
dereferenceAndInline().catch(e => {
    console.error(e);
    process.exit(1);
});

// ---- helpers ----

function isObject(v: any): v is Record<string, any> {
    return v !== null && typeof v === 'object' && !Array.isArray(v);
}

function decodePointerSegment(seg: string): string {
    return seg.replace(/~1/g, '/').replace(/~0/g, '~');
}

function getByPointer(root: any, pointer: string): any {
    // pointer may start with '#' or '#/' or '/'
    let p = pointer.startsWith('#') ? pointer.slice(1) : pointer;
    if (p === '') return root;
    if (!p.startsWith('/')) throw new Error(`Unsupported JSON pointer: ${pointer}`);
    const parts = p.split('/').slice(1).map(decodePointerSegment);
    let cur: any = root;
    for (const key of parts) {
        if (cur == null) return undefined;
        cur = cur[key];
    }
    return cur;
}

function deepClone<T>(v: T): T {
    if (Array.isArray(v)) return v.map(x => deepClone(x)) as any;
    if (isObject(v)) {
        const out: Record<string, any> = {};
        for (const k of Object.keys(v)) out[k] = deepClone((v as any)[k]);
        return out as any;
    }
    return v;
}

function shallowMerge(a: any, b: any): any {
    // return new object with keys from a then b (b overrides)
    const out: Record<string, any> = {};
    if (isObject(a)) Object.assign(out, a);
    if (isObject(b)) Object.assign(out, b);
    return out;
}

function derefLocalRefs<T = any>(node: T, root: any, visiting: Set<string> = new Set()): T {
    if (Array.isArray(node)) {
        return node.map((item) => derefLocalRefs(item, root, visiting)) as any;
    }
    if (!isObject(node)) return node;

    // If this object contains a $ref, inline it (local only)
    if (typeof (node as any).$ref === 'string') {
        const ref: string = (node as any).$ref;
        if (!ref.startsWith('#')) {
            // external ref: leave as-is to avoid fetching
            // but still clone and recurse for other keys
            const { $ref, ...rest } = node as any;
            const restDeref = derefLocalRefs(rest, root, visiting);
            return shallowMerge({ $ref: ref }, restDeref) as any;
        }
        const pointer = ref;
        if (visiting.has(pointer)) {
            // cycle detected; to keep exactness, throw to surface the recursion
            throw new Error(`Cyclic local $ref detected at ${pointer}`);
        }
        visiting.add(pointer);
        const target = getByPointer(root, pointer);
        if (target === undefined) {
            throw new Error(`Unresolvable local $ref: ${pointer}`);
        }
        const targetDeref = derefLocalRefs(target, root, visiting);
        visiting.delete(pointer);

        // Merge: target first, then other sibling keys override, excluding $ref
        const { $ref: _omit, ...siblings } = node as any;
        const siblingsDeref = derefLocalRefs(siblings, root, visiting);
        const merged = shallowMerge(targetDeref, siblingsDeref);
        return merged as any;
    }

    // Otherwise, recurse properties
    const out: Record<string, any> = {};
    for (const [k, v] of Object.entries(node)) {
        out[k] = derefLocalRefs(v, root, visiting);
    }
    return out as any;
}
