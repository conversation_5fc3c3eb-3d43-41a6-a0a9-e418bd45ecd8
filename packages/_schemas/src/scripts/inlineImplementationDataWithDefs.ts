import fs from 'fs';
import path from 'path';

/**
 * Build a self-contained ImplementationData schema instance that includes all transitively referenced local
 * subschemas under its own $defs, while keeping $ref pointers intact. Then inline it as a const in
 * SignatureData.properties.exposedSchema.allOf[1].
 */
async function inlineImplementationDataWithDefs() {
  const projectRoot = process.cwd();
  const inputDir = path.join(projectRoot, 'src', 'schemas');
  const primitivesPath = path.join(inputDir, 'Primitives.json');
  const raw = fs.readFileSync(primitivesPath, 'utf-8');
  const json = JSON.parse(raw);

  const rootDefs = (json as any).$defs;
  if (!rootDefs || typeof rootDefs !== 'object') {
    throw new Error('Root $defs not found');
  }

  const implData = rootDefs.ImplementationData;
  if (!implData) {
    throw new Error('ImplementationData not found in $defs');
  }

  // Collect transitive closure of local defs referenced from ImplementationData
  const needed = collectLocalDefClosure(implData, rootDefs);

  // Build a self-contained schema instance: ImplementationData + embedded $defs subset
  const implClone = deepClone(implData);
  const defsOut: Record<string, any> = {};
  for (const name of needed) {
    if (name === 'ImplementationData') continue; // avoid self-inclusion
    const def = rootDefs[name];
    if (def === undefined) {
      throw new Error(`Referenced local def not found in root: ${name}`);
    }
    defsOut[name] = deepClone(def);
  }

  // Merge into a single schema instance; if $defs already exists, merge with precedence to collected ones
  const existingDefs = isObject(implClone.$defs) ? implClone.$defs : {};
  implClone.$defs = { ...existingDefs, ...defsOut };

  // Insert as const at SignatureData.properties.exposedSchema.allOf[1]
  const sigData = (json as any).$defs?.SignatureData;
  if (!sigData) throw new Error('SignatureData not found');
  const exposedSchema = sigData.allOf?.[1]?.properties?.exposedSchema;
  if (!exposedSchema || !Array.isArray(exposedSchema.allOf)) throw new Error('exposedSchema.allOf not found');

  exposedSchema.allOf[1] = { const: implClone };

  // Optional: update comment to reflect embedding strategy
  const cmt = 'The const equals ImplementationData but self-contained with all transitively used local $defs embedded.';
  if (typeof exposedSchema.$comment !== 'string' || !exposedSchema.$comment.includes('const')) {
    exposedSchema.$comment = cmt;
  }

  fs.writeFileSync(primitivesPath, JSON.stringify(json, null, 2));
  console.log('Primitives.json updated: inlined ImplementationData const with embedded $defs.');
}

// Run as script (ESM)
inlineImplementationDataWithDefs().catch((e) => {
  console.error(e);
  process.exit(1);
});

// ---- helpers ----

function isObject(v: any): v is Record<string, any> {
  return v !== null && typeof v === 'object' && !Array.isArray(v);
}

function deepClone<T>(v: T): T {
  if (Array.isArray(v)) return v.map((x) => deepClone(x)) as any;
  if (isObject(v)) {
    const out: Record<string, any> = {};
    for (const k of Object.keys(v)) out[k] = deepClone((v as any)[k]);
    return out as any;
  }
  return v;
}

function extractLocalDefName(ref: string): string | null {
  // Accept refs like '#/$defs/Name' only (single-level under $defs)
  if (!ref || !ref.startsWith('#/')) return null;
  const parts = ref.slice(2).split('/'); // drop '#/'
  if (parts.length !== 2) return null;
  if (parts[0] !== '$defs') return null;
  // JSON pointer decoding for the def name
  const name = parts[1].replace(/~1/g, '/').replace(/~0/g, '~');
  return name;
}

function collectLocalDefClosure(node: any, rootDefs: Record<string, any>): Set<string> {
  const needed = new Set<string>();
  const queue: string[] = [];

  function visit(n: any) {
    if (Array.isArray(n)) {
      for (const item of n) visit(item);
      return;
    }
    if (!isObject(n)) return;
    if (typeof n.$ref === 'string') {
      const name = extractLocalDefName(n.$ref);
      if (name && !needed.has(name)) {
        needed.add(name);
        queue.push(name);
      }
    }
    for (const val of Object.values(n)) visit(val);
  }

  visit(node);

  while (queue.length > 0) {
    const name = queue.shift()!;
    const def = rootDefs[name];
    if (!def) continue; // missing defs handled later at copy time
    visit(def);
  }

  return needed;
}
