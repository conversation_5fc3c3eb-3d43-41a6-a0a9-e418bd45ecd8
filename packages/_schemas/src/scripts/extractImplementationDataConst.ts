import fs from 'fs';
import path from 'path';

/**
 * Extracts the const object under:
 * $defs.SignatureData.allOf[1].properties.exposedSchema.allOf[1].const
 * and writes it as a standalone schema JSON file in src/schemas.
 */
async function extractImplementationDataConst() {
    const projectRoot = process.cwd();
    const schemasDir = path.join(projectRoot, 'src', 'schemas');
    const primitivesPath = path.join(schemasDir, 'Primitives.json');
    const outPath = path.join(schemasDir, 'ImplementationData.schema.json');

    if (!fs.existsSync(primitivesPath)) {
        throw new Error(`Input file not found: ${primitivesPath}`);
    }

    const raw = fs.readFileSync(primitivesPath, 'utf-8');
    const json = JSON.parse(raw);

    const sigData = json?.$defs?.SignatureData;
    if (!sigData) throw new Error('Path not found: $defs.SignatureData');

    const exposedSchema = sigData?.allOf?.[1]?.properties?.exposedSchema;
    if (!exposedSchema || !Array.isArray(exposedSchema.allOf)) {
        throw new Error('Path not found: $defs.SignatureData.allOf[1].properties.exposedSchema.allOf');
    }

    const constSchema = exposedSchema.allOf?.[1]?.const;
    if (!constSchema || typeof constSchema !== 'object') {
        throw new Error('Path not found: exposedSchema.allOf[1].const (expected an object)');
    }

    fs.writeFileSync(outPath, JSON.stringify(constSchema, null, 2) + '\n');
    console.log(`Wrote ImplementationData schema to: ${outPath}`);
}

// Run as script (ESM)
extractImplementationDataConst().catch((e) => {
    console.error(e);
    process.exit(1);
});
