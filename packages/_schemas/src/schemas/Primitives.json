{"$id": "https://schemas.toolproof.com/v0/Primitives.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "$defs": {"BranchStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"cases": {"type": "array", "items": {"$ref": "#/$defs/ConditionalWrapper"}, "minItems": 1, "uniqueItems": true}, "id": {"$ref": "#/$defs/BranchStepId"}, "kind": {"const": "branch"}}, "required": ["id", "kind", "case"]}], "$anchor": "BranchStep"}, "BranchStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "BranchStepId", "pattern": "^BRANCHSTEP-.+$"}, "ConditionalWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"what": {"$ref": "#/$defs/WorkStep"}, "when": {"$ref": "#/$defs/WorkStep"}}, "required": ["when", "what"], "unevaluatedProperties": false, "$anchor": "ConditionalWrapper"}, "Description": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Description"}, "Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}], "$anchor": "Documented"}, "Execution": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"id": {"$ref": "#/$defs/ExecutionId"}, "implementationId": {"$ref": "#/$defs/ImplementationId"}, "resourceBindingMap": {"type": "object", "properties": {"inputBindingMap": {"$comment": "This will be overlayed at runtime to specify an inputBindingMap corresponding to the inputMap of the underlying implementation.", "$ref": "#/$defs/ResourceBindingMap"}, "outputBindingMap": {"$comment": "This will be overlayed at runtime to specify an outputBindingMap corresponding to the outMap of the underlying implementation.", "$ref": "#/$defs/ResourceBindingMap"}}, "required": ["inputBindingMap", "outputBindingMap"], "unevaluatedProperties": false}}, "required": ["id", "implementationId", "resourceBindingMap"]}], "$anchor": "Execution"}, "ExecutionId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ExecutionId", "pattern": "^EXECUTION-.+$"}, "ExposedSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"anyOf": [{"required": ["$id"]}, {"required": ["$anchor"]}, true]}, {"oneOf": [{"properties": {"allOf": {"type": "array", "contains": {"type": "object", "required": ["required", "properties"]}, "uniqueItems": true}}, "required": ["allOf"]}, {"required": ["required", "properties"]}]}, {"anyOf": [{"properties": {"additionalProperties": {"const": false}}, "required": ["additionalProperties"]}, {"properties": {"unevaluatedProperties": {"const": false}}, "required": ["unevaluatedProperties"]}]}], "properties": {"$id": {"type": "string", "format": "uri"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"semanticIdentity": {"$ref": "#/$defs/SemanticIdentityProp"}, "semanticMerit": {"$ref": "#/$defs/SemanticMeritProp"}}}, "required": {"type": "array", "items": {"type": "string"}, "minItems": 1, "uniqueItems": true}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}, "envelopedSemantics": {"type": "object", "properties": {"semanticIdentity": {"$ref": "#/$defs/SemanticIdentityProp"}, "semanticMerit": {"$ref": "#/$defs/SemanticMeritProp"}}, "$comment": "This is to allow semantic properties to be nested under a stable property name instead of at the root level. This is used by ImplementationDetails but could be used by other types (also user-created types) in the future."}}, "required": ["$schema", "type"], "unevaluatedProperties": false, "$anchor": "ExposedSchema"}, "ForStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"case": {"$ref": "#/$defs/ConditionalWrapper"}, "id": {"$ref": "#/$defs/ForStepId"}, "kind": {"const": "for"}}, "required": ["id", "kind", "case"]}], "$anchor": "ForStep"}, "ForStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ForStepId", "pattern": "^FORSTEP-.+$"}, "FormatBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"properties": {"id": {"$ref": "#/$defs/FormatId"}}, "required": ["id"]}], "$anchor": "FormatBase"}, "FormatData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}], "unevaluatedProperties": false, "$anchor": "FormatData"}, "FormatId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "FormatId", "pattern": "^FORMAT-.+$"}, "FormatMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false, "$anchor": "FormatMeta"}, "Identifiable": {"type": "object", "required": ["id"], "$anchor": "Identifiable"}, "IdentifiableDocumented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/Documented"}], "$anchor": "IdentifiableDocumented"}, "ImplementationBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"type": "object", "properties": {"id": {"$ref": "#/$defs/ImplementationId"}, "roleMap": {"type": "object", "properties": {"inputMap": {"$comment": "This will be overlayed at runtime to specify an inputMap corresponding to the inputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}, "outputMap": {"$comment": "This will be overlayed at runtime to specify an outputMap corresponding to the outputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}}, "required": ["inputMap", "outputMap"]}, "signatureId": {"$ref": "#/$defs/SignatureId"}}, "required": ["id", "signatureId", "roleMap"]}], "$anchor": "ImplementationBase"}, "ImplementationData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This is stable across versions of the implementation.", "$ref": "#/$defs/ImplementationBase"}, {"$comment": "This is variable across versions of the implementation.", "$ref": "#/$defs/ImplementationDetails"}], "$anchor": "ImplementationData"}, "ImplementationDetails": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"envelopedSemantics": {"type": "object", "properties": {"semanticIdentity": {"type": "string", "$comment": "This will be the commit hash of the implementation's repository at the time of creating or updating the implementation.", "pattern": "^[0-9a-f]{7,40}$"}}, "required": ["semanticIdentity"], "unevaluatedProperties": false}}, "required": ["envelopedSemantics"], "$anchor": "ImplementationDetails"}, "ImplementationId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ImplementationId", "pattern": "^IMPLEMENTATION-.+$"}, "ImplementationMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ImplementationBase"}, {"$ref": "#/$defs/Path"}, {"$comment": "This points to the actual code implementation in ToolProof's Kubernetes cluster.", "$ref": "#/$defs/Uri"}], "$anchor": "ImplementationMeta"}, "ImplementationRoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/RoleLiteral"}, "$anchor": "ImplementationRoleMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "JsonValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonValue"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonValue"}}], "$anchor": "JsonValue"}, "Name": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^[A-Z].*", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Name"}, "Path": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"path": {"type": "string", "format": "uri"}}, "required": ["path"], "$anchor": "Path"}, "ResourceBindingMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceId"}, "$anchor": "ResourceBindingMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "ResourceId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceId", "$comment": "This should be generated in the build process to keep the definitions DRY. We should also consider having an explicit ResourceId definition (RESOURCE-...) and rather let the ResourceMetas (ResourceMetaActive and ResourceMetaPassive) compose roleId and executionId.", "pattern": "^ROLE-.+__EXECUTION-.+$"}, "ResourceMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceMetaActive"}, "$anchor": "ResourceMap", "propertyNames": {"$ref": "#/$defs/ResourceId"}}, "ResourceMetaActive": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"properties": {"id": {"$ref": "#/$defs/ResourceId"}}, "required": ["id"]}, {"$ref": "#/$defs/Timestamp"}, {"properties": {"exposedData": {"$comment": "This is overlayed at runtime to specify the actual data of the resource.", "$ref": "#/$defs/JsonValue"}}, "required": ["exposedData"]}, {"oneOf": [{"$ref": "#/$defs/Path"}, {"properties": {"pointer": {"$ref": "#/$defs/ResourceId"}}, "required": ["pointer"]}]}], "unevaluatedProperties": false, "$anchor": "ResourceMetaActive"}, "ResourceMetaPassive": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"properties": {"id": {"$ref": "#/$defs/ResourceId"}}, "required": ["id"]}, {"$ref": "#/$defs/Timestamp"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false, "$anchor": "ResourceMetaPassive"}, "RoleId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "RoleId", "pattern": "^ROLE-.+$"}, "RoleLiteral": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"typeId": {"$ref": "#/$defs/TypeId"}}, "required": ["typeId"]}, {"$ref": "#/$defs/Documented"}], "$anchor": "RoleLiteral"}, "SemanticIdentityProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"properties": {"type": {"enum": ["string", "integer", "boolean"]}}, "required": ["type"]}, {"properties": {"enum": {"type": "array", "items": {"type": "string"}, "minItems": 1}}, "required": ["enum"]}], "$anchor": "SemanticIdentityProp"}, "SemanticMeritProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"properties": {"type": {"enum": ["number", "integer"]}}, "required": ["type"]}, {"properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}, "required": ["enum"]}], "$anchor": "SemanticMeritProp"}, "SignatureBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"properties": {"formatId": {"$comment": "We have formatId here in order to make signatures type-like.", "const": "FORMAT-ApplicationJob"}, "id": {"$ref": "#/$defs/SignatureId"}, "roleMap": {"type": "object", "properties": {"inputMap": {"$ref": "#/$defs/SignatureRoleMap"}, "outputMap": {"$ref": "#/$defs/SignatureRoleMap"}}, "required": ["inputMap", "outputMap"]}}, "required": ["id", "formatId", "roleMap"]}], "$anchor": "SignatureBase"}, "SignatureData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/SignatureBase"}, {"properties": {"exposedSchema": {"allOf": [{"$ref": "#/$defs/ExposedSchema"}, {"const": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This is stable across versions of the implementation.", "$ref": "#/$defs/ImplementationBase"}, {"$comment": "This is variable across versions of the implementation.", "$ref": "#/$defs/ImplementationDetails"}], "$anchor": "ImplementationData", "$defs": {"ImplementationBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"type": "object", "properties": {"id": {"$ref": "#/$defs/ImplementationId"}, "roleMap": {"type": "object", "properties": {"inputMap": {"$comment": "This will be overlayed at runtime to specify an inputMap corresponding to the inputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}, "outputMap": {"$comment": "This will be overlayed at runtime to specify an outputMap corresponding to the outputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}}, "required": ["inputMap", "outputMap"]}, "signatureId": {"$ref": "#/$defs/SignatureId"}}, "required": ["id", "signatureId", "roleMap"]}], "$anchor": "ImplementationBase"}, "ImplementationDetails": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"envelopedSemantics": {"type": "object", "properties": {"semanticIdentity": {"type": "string", "$comment": "This will be the commit hash of the implementation's repository at the time of creating or updating the implementation.", "pattern": "^[0-9a-f]{7,40}$"}}, "required": ["semanticIdentity"], "unevaluatedProperties": false}}, "required": ["envelopedSemantics"], "$anchor": "ImplementationDetails"}, "IdentifiableDocumented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/Documented"}], "$anchor": "IdentifiableDocumented"}, "ImplementationId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ImplementationId", "pattern": "^IMPLEMENTATION-.+$"}, "ImplementationRoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/RoleLiteral"}, "$anchor": "ImplementationRoleMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "SignatureId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "SignatureId", "pattern": "^SIGNATURE-.+$"}, "Identifiable": {"type": "object", "required": ["id"], "$anchor": "Identifiable"}, "Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}], "$anchor": "Documented"}, "RoleLiteral": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"typeId": {"$ref": "#/$defs/TypeId"}}, "required": ["typeId"]}, {"$ref": "#/$defs/Documented"}], "$anchor": "RoleLiteral"}, "RoleId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "RoleId", "pattern": "^ROLE-.+$"}, "Name": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^[A-Z].*", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Name"}, "Description": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Description"}, "TypeId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "TypeId", "pattern": "^TYPE-.+$"}}}}], "$comment": "The const is generated in the build process (via extractImplementationDataConst.ts and inlineImplementationDataWithDefs.ts) and it equals the extracted subschema (with all its direct and indirect dependencies inlined as $defs) at #defs/ImplementationData. This means that exposedSchema must both satisfy #defs/ExposedSchema and be the exact schema (seen as a data instance!) defined at #defs/ImplementationData. It also means that ImplementationData must itself satisfy ExposedSchema."}}, "$comment": "We're not requiring exposedSchema here for now"}], "unevaluatedProperties": false, "$anchor": "SignatureData"}, "SignatureId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "SignatureId", "pattern": "^SIGNATURE-.+$"}, "SignatureMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/SignatureBase"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false, "$anchor": "SignatureMeta"}, "SignatureRoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/TypeId"}, "$anchor": "SignatureRoleMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "Step": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false, "$anchor": "Step"}, "StepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStepId"}, {"$ref": "#/$defs/BranchStepId"}, {"$ref": "#/$defs/WhileStepId"}, {"$ref": "#/$defs/ForStepId"}], "$anchor": "StepId"}, "Timestamp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"timestamp": {"type": "string", "default": "1970-01-01T00:00:00Z", "format": "date-time"}}, "required": ["timestamp"], "$anchor": "Timestamp"}, "TypeBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"properties": {"formatId": {"$ref": "#/$defs/FormatId"}, "id": {"$ref": "#/$defs/TypeId"}}, "required": ["id", "formatId"]}], "$anchor": "TypeBase"}, "TypeData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"properties": {"exposedSchema": {"$ref": "#/$defs/ExposedSchema"}}, "required": ["exposedSchema"]}], "$anchor": "TypeData"}, "TypeId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "TypeId", "pattern": "^TYPE-.+$"}, "TypeMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"$ref": "#/$defs/Path"}, {"if": {"properties": {"formatId": {"const": "FORMAT-ApplicationJson"}}, "$comment": "If formatId is FORMAT-ApplicationJson, then uri must not be present, but if formatId is not FORMAT-ApplicationJson, then uri must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor."}, "then": {"not": {"$ref": "#/$defs/Uri"}}, "else": {"$ref": "#/$defs/Uri"}}], "unevaluatedProperties": false, "$anchor": "TypeMeta"}, "Uri": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"uri": {"type": "string", "format": "uri"}}, "required": ["uri"], "$anchor": "<PERSON><PERSON>"}, "WhileStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"case": {"$ref": "#/$defs/ConditionalWrapper"}, "id": {"$ref": "#/$defs/WhileStepId"}, "kind": {"const": "while"}}, "required": ["id", "kind", "case"]}], "$anchor": "WhileStep"}, "WhileStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WhileStepId", "pattern": "^WHILESTEP-.+$"}, "WorkStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"execution": {"$ref": "#/$defs/Execution"}, "id": {"$ref": "#/$defs/WorkStepId"}, "kind": {"const": "work"}}, "required": ["id", "kind", "execution"]}], "$anchor": "WorkStep"}, "WorkStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkStepId", "pattern": "^WORKSTEP-.+$"}, "Workflow": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"id": {"$ref": "#/$defs/WorkflowId"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}, "required": ["id", "steps"]}], "unevaluatedProperties": false, "$anchor": "Workflow"}, "WorkflowId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkflowId", "pattern": "^WORKFLOW-.+$"}, "WorkflowSpec": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"properties": {"id": {"$ref": "#/$defs/WorkflowSpecId"}, "resourceMaps": {"type": "array", "items": {"$ref": "#/$defs/ResourceMap"}, "uniqueItems": true}, "workflow": {"$ref": "#/$defs/Workflow"}}, "required": ["id", "workflow", "resourceMaps"]}], "$anchor": "WorkflowSpec"}, "WorkflowSpecId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkflowSpecId", "pattern": "^WORKFLOWSPEC-.+$"}}, "$comment": "This schema defines all primitive schemas used throughout the Toolproof ecosystem. The primitive schemas themselves are defined in $defs.<Primitive>.exposedSchema. The build process (via extractSchemas.js) extracts these schemas and writes them to a separate file (src/schemas/Primitives.json). The reason for this indirection is to have all these schema envelopes validate positively against the schema at $defs/TypeData, effectively making them ResourceTypes, which are first-class citizens in the Toolproof ecosystem. Be aware that the terms ResourceFormat, ResourceType, and ResourceRole used in ToolProof's official documentation correspond to Format, Type, and Role here and throughout the codebase. Also, the terms JobSignature, JobImplementation, and JobExecution in the documentation correspond to Signature, Implementation, and Execution here and throughout the codebase. This is to avoid long names in the codebase."}