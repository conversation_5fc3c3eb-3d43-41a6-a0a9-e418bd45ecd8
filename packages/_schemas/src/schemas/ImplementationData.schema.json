{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$comment": "This is stable across versions of the implementation.", "$ref": "#/$defs/ImplementationBase"}, {"$comment": "This is variable across versions of the implementation.", "$ref": "#/$defs/ImplementationDetails"}], "$anchor": "ImplementationData", "$defs": {"ImplementationBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"type": "object", "properties": {"id": {"$ref": "#/$defs/ImplementationId"}, "roleMap": {"type": "object", "properties": {"inputMap": {"$comment": "This will be overlayed at runtime to specify an inputMap corresponding to the inputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}, "outputMap": {"$comment": "This will be overlayed at runtime to specify an outputMap corresponding to the outputMap of the underlying signature.", "$ref": "#/$defs/ImplementationRoleMap"}}, "required": ["inputMap", "outputMap"]}, "signatureId": {"$ref": "#/$defs/SignatureId"}}, "required": ["id", "signatureId", "roleMap"]}], "$anchor": "ImplementationBase"}, "ImplementationDetails": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"envelopedSemantics": {"type": "object", "properties": {"semanticIdentity": {"type": "string", "$comment": "This will be the commit hash of the implementation's repository at the time of creating or updating the implementation.", "pattern": "^[0-9a-f]{7,40}$"}}, "required": ["semanticIdentity"], "unevaluatedProperties": false}}, "required": ["envelopedSemantics"], "$anchor": "ImplementationDetails"}, "IdentifiableDocumented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/Documented"}], "$anchor": "IdentifiableDocumented"}, "ImplementationId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ImplementationId", "pattern": "^IMPLEMENTATION-.+$"}, "ImplementationRoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/RoleLiteral"}, "$anchor": "ImplementationRoleMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "SignatureId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "SignatureId", "pattern": "^SIGNATURE-.+$"}, "Identifiable": {"type": "object", "required": ["id"], "$anchor": "Identifiable"}, "Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}], "$anchor": "Documented"}, "RoleLiteral": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"typeId": {"$ref": "#/$defs/TypeId"}}, "required": ["typeId"]}, {"$ref": "#/$defs/Documented"}], "$anchor": "RoleLiteral"}, "RoleId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "RoleId", "pattern": "^ROLE-.+$"}, "Name": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^[A-Z].*", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Name"}, "Description": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Description"}, "TypeId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "TypeId", "pattern": "^TYPE-.+$"}}}