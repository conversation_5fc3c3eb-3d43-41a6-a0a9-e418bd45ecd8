export { default as PrimitivesSchema } from './schemas/Primitives.json' with { type: 'json' };

import type { FormatData, TypeData, SignatureData, ImplementationData, FormatMeta, TypeMeta, SignatureMeta, ImplementationMeta, RoleId, RoleLiteral } from './types/types.d.ts';

type Role = { id: RoleId } & RoleLiteral;

type PrimitiveData = FormatData | TypeData | SignatureData | ImplementationData;
type PrimitiveMeta = FormatMeta | TypeMeta | SignatureMeta | ImplementationMeta;

export type {
  Role as RoleJson,
  PrimitiveData as PrimitiveDataJson,
  PrimitiveMeta as PrimitiveMetaJson
};

export type {
  Identifiable as IdentifiableJson,
  Documented as Documented<PERSON>son,
  IdentifiableDocumented as IdentifiableDocumented<PERSON>son,
  FormatId as FormatIdJson,
  FormatMeta as FormatMetaJson,
  FormatData as FormatDataJson,
  ExposedSchema as ExposedSchemaJson,
  SemanticIdentityProp as SemanticIdentityPropJson,
  SemanticMeritProp as SemanticMeritProp<PERSON>son,
  TypeId as <PERSON>Id<PERSON>son,
  TypeMeta as TypeMeta<PERSON>son,
  TypeData as TypeData<PERSON>son,
  RoleId as RoleId<PERSON>son,
  RoleLiteral as RoleLiteralJson,
  SignatureRoleMap as SignatureRoleMapJson,
  ImplementationRoleMap as ImplementationRoleMapJson,
  SignatureId as SignatureIdJson,
  SignatureMeta as SignatureMetaJson,
  SignatureData as SignatureDataJson,
  ImplementationId as ImplementationIdJson,
  ImplementationMeta as ImplementationMetaJson,
  ImplementationData as ImplementationDataJson,
  ExecutionId as ExecutionIdJson,
  Execution as ExecutionJson,
  ConditionalWrapper as ConditionalWrapperJson,
  ResourceBindingMap as ResourceBindingMapJson,
  ResourceId as ResourceIdJson,
  WorkStep as WorkStepJson,
  BranchStep as BranchStepJson,
  WhileStep as WhileStepJson,
  ForStep as ForStepJson,
  StepId as StepIdJson,
  Step as StepJson,
  ResourceMetaActive as ResourceMetaActiveJson,
  ResourceMetaPassive as ResourceMetaPassiveJson,
  ResourceMap as ResourceMapJson,
  Workflow as WorkflowJson,
  WorkflowSpec as WorkflowSpecJson
} from './types/types.d.ts';
