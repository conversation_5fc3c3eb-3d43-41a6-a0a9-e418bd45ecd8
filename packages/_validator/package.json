{"name": "@toolproof/_validator", "version": "1.0.0", "type": "module", "main": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "types": "./dist/index.d.ts", "scripts": {"dev": "node --loader ts-node/esm src/index.ts", "build": "tsc", "start": "node ./dist/index.js", "generateStandalone": "node ./dist/scripts/generateStandalone.js && node ./dist/scripts/formatStandalone.js", "formatStandalone": "node ./dist/scripts/formatStandalone.js", "testStandalone": "node --loader ts-node/esm src/scripts/testStandalone.ts"}, "dependencies": {"@toolproof/_lib": "workspace:*", "@toolproof/_schemas": "workspace:*", "ajv": "^8.17.1", "ajv-formats": "^3.0.1"}, "devDependencies": {"@types/node": "^20.19.11", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.0.0"}}