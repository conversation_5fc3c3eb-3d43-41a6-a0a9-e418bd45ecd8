import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import { createRequire } from 'node:module';
import fs from 'node:fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function loadValidator() {
  const validatorPath = path.resolve(__dirname, '../generated/validateTypeData.js');

  // Prefer requiring the CJS module from ESM
  const require = createRequire(import.meta.url);
  const mod = require(validatorPath);

  // If ts-node transpiles it to ESM somehow, fall back to dynamic import
  const fn =
    (typeof mod === 'function' ? mod : mod?.default) ??
    (await import(pathToFileURL(validatorPath).href)).default;

  if (typeof fn !== 'function') {
    throw new Error(`validateTypeData did not export a function from: ${validatorPath}`);
  }
  return fn as ((data: unknown) => boolean) & { errors?: unknown };
}

function printResult(ok: boolean, errors: unknown) {
  if (ok) {
    console.log('Valid ✅');
  } else {
    console.log('Invalid ❌');
    console.dir(errors ?? null, { depth: null });
  }
}

async function main() {
  const validate = await loadValidator();

  const fileArg = process.argv[2];
  if (fileArg) {
    const filePath = path.resolve(process.cwd(), fileArg);
    const json = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const ok = validate(json);
    printResult(ok, (validate as any).errors);
    process.exit(ok ? 0 : 1);
  }

  const sampleOk = {
    id: 'TYPE-MyType',
    name: 'My type',
    formatId: 'FORMAT-Json',
    exposedSchema: {
      $schema: 'https://json-schema.org/draft/2020-12/schema',
      type: 'object',
      $id: 'https://example.com/schemas/my-type.json',
      required: ['foo'],
      properties: { foo: { type: 'string' } },
      unevaluatedProperties: false,
    },
  };
  console.log('--- Valid sample ---');
  printResult(validate(sampleOk), (validate as any).errors);

  const sampleBad = {
    id: 'TYPE-MyType',
    name: 'X',
    formatId: 'FORMAT-Json',
    exposedSchema: {
      $schema: 'https://json-schema.org/draft/2020-12/schema',
      type: 'object',
      $anchor: 'MyAnchor',
      required: ['foo'],
      properties: { foo: { type: 'string' } },
      unevaluatedProperties: false,
    },
  };
  console.log('\n--- Invalid sample ---');
  printResult(validate(sampleBad), (validate as any).errors);
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});