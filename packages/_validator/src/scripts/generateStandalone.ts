import { PrimitivesSchema } from '@toolproof/_schemas';
import { CONSTANTS } from '@toolproof/_lib/constants';
import { getPrimitiveRef, getPrimitiveValidator } from '../compilers.js';
// NodeNext/ESM requires explicit extension for subpath imports
import standaloneCode from 'ajv/dist/standalone/index.js';
import fs from 'fs';
import path from 'path';

const subSchema = CONSTANTS.SCHEMA.TypeData;

export function generateStandalone() {
    const ref = getPrimitiveRef(subSchema);
    console.log(`ref: ${ref}`);
    const schema = { $ref: ref };
    const { validate, ajv } = getPrimitiveValidator(schema, `__ref:${ref}`);
    if (!validate) {
        throw new Error(`Schema not found in Ajv: ${PrimitivesSchema.$id}`);
    }
    // IMPORTANT: pass the original compiled function; binding removes the `.source` Ajv attaches
    const moduleCode = standaloneCode(ajv, validate);

    console.log('Generated standalone validator code:');
    // console.log(moduleCode);

    // Ensure target directory exists: write into src/generated
    const outDir = path.join(process.cwd(), 'src', 'generated');
    const outFile = path.join(outDir, `validate${subSchema}.js`);
    fs.mkdirSync(outDir, { recursive: true });
    fs.writeFileSync(outFile, moduleCode, 'utf8');

}

generateStandalone();