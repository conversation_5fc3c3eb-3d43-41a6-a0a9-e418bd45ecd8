import fs from 'node:fs/promises';
import path from 'node:path';
import prettier from 'prettier';

async function formatFile(filePath: string): Promise<boolean> {
  const source = await fs.readFile(filePath, 'utf8');
  // Use Prettier config if present; filepath helps infer the right parser
  const config = (await prettier.resolveConfig(filePath)) ?? {};
  const formatted = await prettier.format(source, { ...config, filepath: filePath });
  if (formatted !== source) {
    await fs.writeFile(filePath, formatted, 'utf8');
    return true;
  }
  return false;
}

async function* walk(dir: string): AsyncGenerator<string> {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const full = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      yield* walk(full);
    } else {
      yield full;
    }
  }
}

async function main(): Promise<void> {
  const targetDir = path.join(process.cwd(), 'src', 'generated');
  let formattedCount = 0;
  try {
    for await (const file of walk(targetDir)) {
      if (file.endsWith('.js') || file.endsWith('.mjs')) {
        const changed = await formatFile(file);
        if (changed) formattedCount++;
      }
    }
    console.log(`Beautified ${formattedCount} file(s) in ${targetDir}`);
  } catch (err) {
    const e = err as NodeJS.ErrnoException;
    if (e?.code === 'ENOENT') {
      console.warn(`Directory not found: ${targetDir}`);
      process.exit(0);
    }
    console.error(err);
    process.exit(1);
  }
}

void main();
