
export function jsonPointerUnescape(seg: string): string {
    return seg.replace(/~1/g, '/').replace(/~0/g, '~');
}

export function normalizeDefsPointer(p: string) {
    if (p === '#') return '#';                  // top-level schema
    if (p.startsWith('#')) return p;            // "#/$defs/FormatData"
    if (p.startsWith('/')) return `#${p}`;      // "/$defs/FormatData"
    if (p.startsWith('$defs/')) return `#/${p}`;// "$defs/FormatData"
    return `#/$defs/${p}`;                      // "FormatData" => "#/$defs/FormatData"
}

// Remove $anchor keys recursively and rewrite internal anchor-style refs (#Name) to JSON Pointers (#/$defs/Name)
// ATTENTION: Is this necessary with Ajv2020?
export function stripAnchorsAndRewriteRefs<T extends Record<string, unknown>>(schema: T): T {
    const seen = new WeakSet<object>();
    type WithDefs = { $defs?: Record<string, unknown> };
    const sObj = (schema as unknown) as WithDefs;
    const defs = new Set(
        sObj && typeof sObj === 'object' && sObj.$defs && typeof sObj.$defs === 'object'
            ? Object.keys(sObj.$defs)
            : []
    );

    function rewriteRef(value: string, isRoot: boolean): string {
        if (/^https?:\/\//.test(value)) return value;
        const m = value.match(/^#([A-Za-z][A-Za-z0-9_-]*)$/);
        if (m) {
            const name = m[1];
            if (defs.has(name)) {
                return `#/$defs/${name}`;
            }
        }
        if (isRoot && value === '#ResourceType') return '#/$defs/ResourceType'; // ATTENTION: legacy name
        return value;
    }

    function walk(node: unknown, isRoot = false): unknown {
        if (Array.isArray(node)) return node.map((child) => walk(child));
        if (node && typeof node === 'object') {
            if (seen.has(node as object)) return node;
            seen.add(node as object);
            const out: Record<string, unknown> = {};
            for (const [k, v] of Object.entries(node as Record<string, unknown>)) {
                if (k === '$anchor') continue;
                if (k === '$ref' && typeof v === 'string') {
                    out[k] = rewriteRef(v, isRoot);
                } else {
                    out[k] = walk(v, false);
                }
            }
            return out;
        }
        return node;
    }
    return walk(schema, true) as T;
}