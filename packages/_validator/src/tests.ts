/* import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>son, JobStepJson } from '@toolproof/_schemas';
import { JobBaseSchema, JobStepSchema, ResourceBindingSchema } from '@toolproof/_schemas';
import { validateJob, validateJobStep } from './validators.js';
import { listPrimitives } from '@toolproof/_lib';
import * as CONSTANTS from '@toolproof/_lib/constants';

export async function testJobSchemaWithOverlay() {

    console.log('JobBaseSchema', JSON.stringify(JobBaseSchema, null, 2));

    return;

    const job: JobJson = {
        "id": "JOB-3poYSaxc0XIKnhFZwkHo",
        "name": "less-than",
        "isPredicate": true,
        "isInternal": true,
        "resources": {
            "inputs": [
                "RER-vjQCXtDdpkUgvTrUF766",
                "RER-FAPcNx0TYeGOBJuyACzs"
            ],
            "outputs": [
                "RER-yRzZa0sKlCcW1dYfbukr"
            ]
        }
    };

    const result = await listPrimitives<ResourceRoleMetaJson>(CONSTANTS.ROLES);
    if (!result.ok) {
        throw new Error('Failed to list resourceRoles');
    }
    const resourceRoles = result.items || [];

    // console.log('validateJob', validateJob(job, resourceRoles));

}

export async function testJobStepSchemaWithOverlay() {
    // console.log('JobStepSchema', JSON.stringify(JobStepSchema, null, 2));
    // console.log('ResourceBindingSchema', JSON.stringify(ResourceBindingSchema, null, 2));

    // return;

    const jobStep: JobStepJson = {
        "id": "JOS-nwU9ALw1pNfSzAJopzvq",
        "kind": "job",
        "jobId": "JOB-APEKPlFfdnPKxWR9hsvd",
        "resourceBindings": {
            "inputBindings": {
                "RER-B5tg4WGnbiGaN1DHE077": "RER-B5tg4WGnbiGaN1DHE077__JOS-nwU9ALw1pNfSzAJopzvq",
                "RER-FAPcNx0TYeGOBJuyACzs": "RER-FAPcNx0TYeGOBJuyACzs__JOS-nwU9ALw1pNfSzAJopzvq"
            },
            "outputBindings": {
                "RER-13bTni46ZIs6FhqglRQY": "RER-13bTni46ZIs6FhqglRQY__JOS-nwU9ALw1pNfSzAJopzvq"
            }
        }
    };

    const result = await listPrimitives<JobJson>(CONSTANTS.JOBS);
    if (!result.ok) {
        throw new Error('Failed to list resourceRoles');
    }
    const jobs = result.items || [];

    console.log('validateJob', validateJobStep(jobStep, jobs));
} */