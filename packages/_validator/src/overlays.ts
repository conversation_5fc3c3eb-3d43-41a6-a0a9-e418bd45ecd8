import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, SignatureMeta<PERSON>son, WorkStepJson } from '@toolproof/_schemas';
import type { Schema<PERSON>ike, AddPatch } from './types/types.js';
import * as CONSTANTS from '@toolproof/_lib/constants';
// import { CONSTANTS } from '@toolproof/_lib/constants';
import { getPrimitiveRef } from './compilers.js';
import { jsonPointerUnescape } from './utils.js';


/* export function generateJobSchemaOverlay(JobSchema: SchemaLike, roles: RoleMetaJson[], isPredicate: boolean) {

    const overlay: any = JSON.parse(JSON.stringify(JobSchema));

    let patch: Array<AddPatch>;

    if (isPredicate) {
        // Build enum lists of ResourceRoleIds by isPredicate

        const roleIdRef = getPrimitiveRef('RoleId');

        const predicateRoleIds = roles
            .filter(rr => rr.isPredicate === true)
            .map(rr => rr.id);

        const jobRoleIds = roles
            .filter(rr => rr.isPredicate === false)
            .map(rr => rr.id);

        // Defensive: if either list is empty, we still produce a schema but with an impossible constraint
        // so validation correctly fails when required roles are not available.
        const containsPredicate = predicateRoleIds.length
            ? { contains: { enum: predicateRoleIds }, minContains: 1 }
            : { contains: { enum: ['__NO_PREDICATE_ROLE_IDS__'] }, minContains: 1 };

        const containsJob = jobRoleIds.length
            ? { contains: { enum: jobRoleIds }, minContains: 1 }
            : { contains: { enum: ['__NO_JOB_ROLE_IDS__'] }, minContains: 1 };


        // Clone base schema shallowly and overlay the predicate (then) branch to constrain inputs

        // Ensure structure exists; per contract, allOf must be present, else schema is corrupted
        if (!Array.isArray(overlay.allOf)) {
            throw new Error('Job schema missing required allOf array');
        }

        // Build a JSON Patch to append our conditional overlay
        patch = [
            {
                op: 'add',
                path: '/allOf/-',
                value: {
                    properties: {
                        resources: {
                            type: 'object',
                            properties: {
                                // inputs constraint: both contains must hold; use allOf to combine
                                inputs: {
                                    type: 'array',
                                    items: { $ref: roleIdRef },
                                    uniqueItems: true,
                                    allOf: [containsJob, containsPredicate],
                                },
                                // outputs constraint: exactly one specific output role
                                outputs: {
                                    type: 'array',
                                    items: { const: CONSTANTS.ROLE_EVALUATION },
                                    minItems: 1,
                                    maxItems: 1,
                                },
                            },
                        },
                    }
                },
            },
        ];
    } else {
        // Non-predicate overlay: enforce that inputs and outputs contain only job roles
        const jobRoleIds = roles
            .filter(rr => rr.isPredicate === false)
            .map(rr => rr.id);

        // If there are no job roles available, enforce an impossible constraint so validation fails
        const itemsJobOnly = jobRoleIds.length
            ? { enum: jobRoleIds }
            : { enum: ['__NO_JOB_ROLE_IDS__'] };

        patch = [
            {
                op: 'add',
                path: '/allOf/-',
                value: {
                    if: { properties: { isPredicate: { const: false } } },
                    then: {
                        properties: {
                            resources: {
                                type: 'object',
                                properties: {
                                    inputs: {
                                        type: 'array',
                                        items: itemsJobOnly,
                                        uniqueItems: true,
                                    },
                                    outputs: {
                                        type: 'array',
                                        items: itemsJobOnly,
                                        uniqueItems: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        ];
    }

    applyJsonPatchAddOnly(overlay as Record<string, unknown>, patch);

    return overlay;
}
 */
// Build a dynamic overlay for JobStepSchema so that inputBindings and outputBindings
// must match exactly the inputs/outputs of the referenced job, with values equal to
// `${ResourceRoleId}__${jobStep.id}`.
/* export function generateJobStepSchemaOverlay(JobStepSchema: SchemaLike, jobStep: JobStepJson, jobs: JobMetaJson[]) {
    const overlay: any = JSON.parse(JSON.stringify(JobStepSchema));
    if (!Array.isArray(overlay.allOf)) {
        throw new Error('JobStep schema missing required allOf array');
    }

    const job = jobs.find(j => j.id === jobStep.jobId);
    if (!job) throw new Error(`Job not found for jobStep.jobId=${jobStep.jobId}`);

    // ATTENTION: Overly defensive? Job contract requires resources.inputs and resources.outputs
    const inputs = job?.resources?.inputs as unknown;
    const outputs = job?.resources?.outputs as unknown;
    if (!Array.isArray(inputs) || !Array.isArray(outputs)) {
        throw new Error('Composed job missing resources.inputs or resources.outputs');
    }

    const inputIds = inputs as string[];
    const outputIds = outputs as string[];

    const inputProps: Record<string, { const: string }> = {};
    for (const roleId of inputIds) {
        inputProps[roleId] = { const: `${roleId}__${jobStep.id}` };
    }
    const outputProps: Record<string, { const: string }> = {};
    for (const roleId of outputIds) {
        outputProps[roleId] = { const: `${roleId}__${jobStep.id}` };
    }

    const inputBindingsSchema = {
        type: 'object',
        properties: inputProps,
        required: Object.keys(inputProps),
        additionalProperties: false,
    };

    const outputBindingsSchema = {
        type: 'object',
        properties: outputProps,
        required: Object.keys(outputProps),
        additionalProperties: false,
    };

    const patch: Array<{ op: 'add'; path: string; value: any }> = [
        {
            op: 'add',
            path: '/allOf/-',
            value: {
                type: 'object',
                properties: {
                    resourceBindings: {
                        type: 'object',
                        properties: {
                            inputBindings: inputBindingsSchema,
                            outputBindings: outputBindingsSchema,
                        },
                    },
                },
            },
        },
    ];

    applyJsonPatchAddOnly(overlay as Record<string, unknown>, patch);
    return overlay;
}


// ResourceMap overlay: require that all input-binding ResourceIds from the jobStep appear
// as properties in the ResourceMap instance. Additional properties remain allowed by base schema.
export function generateResourceSchemaMapOverlay(ResourceMapSchema: SchemaLike, jobStep: JobStepJson, roles: RoleMetaJson[], types: TypeDataJson[]) {
    const overlay: any = JSON.parse(JSON.stringify(ResourceMapSchema));
    // ResourceMap base schema may not have allOf; we will add one if missing since we only append
    if (!Array.isArray(overlay.allOf)) overlay.allOf = [];

    // ATTENTION: Overly defensive? JobStep contract requires resourceBindings.inputBindings
    const inputBindings = jobStep?.resourceBindings?.inputBindings ?? {};
    const requiredResourceIds = Object.values(inputBindings) as string[];

    const resourceIdToTypeMap: Record<string, TypeDataJson> = {};
    for (const [key, value] of Object.entries(inputBindings)) {
        const rr = roles.find((rr) => rr.id === key);
        if (!rr) throw new Error(`ResourceRole not found for id=${key}`);
        // ATTENTION: problematic if ResourceRole is generic
        const rt = types.find((rt) => rt.id === rr?.typeId);
        if (!rt) throw new Error(`ResourceType not found for id=${rr?.typeId} (roleId=${rr.id})`);
        resourceIdToTypeMap[value] = rt;
    }

    // console.log('resourceIdToTypeMap:', JSON.stringify(resourceIdToTypeMap, null, 2));

    const propertiesForRequired: Record<string, any> = {};
    for (const rid of requiredResourceIds) {
        propertiesForRequired[rid] = {
            if: {
                "type": "object",
                "required": ["path"],
                "properties": {
                    "path": true
                }
            },
            then: {
                type: 'object',
                required: ['exposedData'],
                properties: {
                    exposedData: resourceIdToTypeMap[rid].exposedSchema
                }
            },
            else: true
        };
    }

    // If no inputs are required, we can skip adding a required block (no-op overlay)
    if (requiredResourceIds.length === 0) return overlay;

    const patch: Array<{ op: 'add'; path: string; value: any }> = [
        {
            op: 'add',
            path: '/allOf/-',
            value: {
                type: 'object',
                required: requiredResourceIds,
                properties: propertiesForRequired,
            },
        },
    ];

    applyJsonPatchAddOnly(overlay as Record<string, unknown>, patch);
    return overlay;
} */

// Minimal JSON Patch applier supporting only the 'add' operation and appending to arrays via '/-'.
// This is sufficient for our overlay use-case and avoids adding a dependency.
function applyJsonPatchAddOnly(target: Record<string, unknown>, ops: AddPatch[]) {
    for (const op of ops) {
        if (op.op !== 'add') throw new Error(`Unsupported JSON Patch op: ${op.op}`);
        const segments = op.path.split('/').slice(1).map(jsonPointerUnescape);
        if (segments.length === 0) {
            throw new Error('JSON Patch add to root is not supported in this context');
        }
        const last = segments[segments.length - 1];
        const parentPath = segments.slice(0, -1);
        let parent: any = target;
        for (const seg of parentPath) {
            if (parent == null || (typeof parent !== 'object')) {
                throw new Error(`JSON Patch path not found: /${parentPath.join('/')}`);
            }
            parent = (parent as any)[seg];
        }
        if (last === '-') {
            if (!Array.isArray(parent)) {
                throw new Error(`JSON Patch append requires array at /${parentPath.join('/')}`);
            }
            parent.push(op.value);
        } else {
            // Support adding a property or inserting into an array at a numeric index
            if (Array.isArray(parent)) {
                const idx = last === '' ? NaN : Number(last);
                if (!Number.isInteger(idx) || idx < 0 || idx > parent.length) {
                    throw new Error(`Invalid array index for JSON Patch add: ${last}`);
                }
                parent.splice(idx, 0, op.value);
            } else if (parent && typeof parent === 'object') {
                (parent as any)[last] = op.value;
            } else {
                throw new Error(`JSON Patch path parent is not an object/array at /${parentPath.join('/')}`);
            }
        }
    }
}
