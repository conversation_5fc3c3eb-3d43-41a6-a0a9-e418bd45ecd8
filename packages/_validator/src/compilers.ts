import type { ExposedSchemaJson } from '@toolproof/_schemas';
import type { SchemaLike } from './types/types.js';
import { PrimitivesSchema } from '@toolproof/_schemas';
import { jsonPointerUnescape, normalizeDefsPointer, stripAnchorsAndRewriteRefs } from './utils.js';
import Ajv2020, { _, KeywordCxt, SchemaObjCxt } from 'ajv/dist/2020.js';
import addFormats from 'ajv-formats';


// Module-level cached Ajv instances per validator to avoid repeated schema registration
// ATTENTION: Consider refactoring to use a single Ajv instance
let ajvPrimitive: any | null = null;
let ajvResource: any | null = null;
// Keep a handle to the cleaned primitives root schema for pointer resolution
let primitivesRootSchemaCleaned: SchemaLike | null = null;

// ESM/TS compatibility helpers for Ajv and ajv-formats default exports
function createAjvInstance() {
    const AjvCtor = (Ajv2020 as any).default ?? (Ajv2020 as any);
    const instance = new AjvCtor({ allErrors: true, strict: false, strictSchema: false, useDefaults: true, passContext: true, code: { source: true } });
    const addFormatsFn = (addFormats as any).default ?? (addFormats as any);
    addFormatsFn(instance);
    // addUIKeyword(instance); // ATTENTION
    return instance;
}

// Cache compiled validators for subschemas under PrimitivesSchema
const typeDefValidatorCache = new Map<string, any>();
const schemaValidatorCache = new WeakMap<object, any>();

export function getAjvPrimitive() {
    if (ajvPrimitive) return ajvPrimitive;
    const a = createAjvInstance();
    // addUIKeyword(a);
    const PrimitivesSchemaCleaned = stripAnchorsAndRewriteRefs(PrimitivesSchema as SchemaLike); 
    a.addSchema(PrimitivesSchema); // ATTENTION
    // console.log('PrimitivesSchema:', JSON.stringify(PrimitivesSchema, null, 2));
    // console.log('PrimitivesSchemaCleaned:', JSON.stringify(PrimitivesSchemaCleaned, null, 2));
    ajvPrimitive = a as unknown as any;
    // keep handle for subschema extraction (read-only)
    primitivesRootSchemaCleaned = PrimitivesSchemaCleaned as SchemaLike;
    // new Ajv instance => clear validator cache tied to it
    typeDefValidatorCache.clear();
    return ajvPrimitive;
}

// Compile (and cache) a validator for a provided schema object.
// Use this when you want to overlay a subschema before compiling.
// Optionally provide cacheKey to reuse compiled validators across structurally-equal schema objects.
export function getPrimitiveValidator(schema: SchemaLike, cacheKey?: string): { validate: any, ajv: any } {
    const ajv = getAjvPrimitive();

    // 1) Cache by object identity
    if (schema && typeof schema === 'object') {
        const cached = schemaValidatorCache.get(schema as object);
        if (cached) {
            // Back-compat: older entries stored the function directly
            return typeof cached === 'function' ? { validate: cached, ajv } : cached;
        }
    }

    // 2) Optional named cache entry
    if (cacheKey) {
        const named = typeDefValidatorCache.get(cacheKey);
        if (named) {
            return typeof named === 'function' ? { validate: named, ajv } : named;
        }
    }

    const validate = ajv.compile(schema);

    const entry = { validate, ajv };
    if (cacheKey) typeDefValidatorCache.set(cacheKey, entry);
    if (schema && typeof schema === 'object') schemaValidatorCache.set(schema as object, entry);

    return entry;
}

export function getAjvResource() {
    if (ajvResource) return ajvResource;
    const a = createAjvInstance();
    ajvResource = a as unknown as any;
    return ajvResource;
}

export function getResourceValidator(exposedSchema: ExposedSchemaJson): any {
    const ajv = getAjvResource();
    return ajv.compile(exposedSchema);
}

// Return the subschema JSON addressed by defPointer.
// Pointer forms accepted: "FormatData", "#/$defs/FormatData", "/$defs/FormatData", "$defs/FormatData", "#"
// By default returns a deep-cloned object to avoid callers mutating the cached schema.
export function getPrimitiveSubschema(defPointer: string, clone: boolean = true): SchemaLike {
    if (defPointer == null || defPointer === '') {
        throw new Error('A JSON Pointer must be provided. Use "#" for the top-level schema or one of: "FormatData", "#/$defs/FormatData", "/$defs/FormatData", "$defs/FormatData".');
    }
    if (!primitivesRootSchemaCleaned) getAjvPrimitive();
    const root = primitivesRootSchemaCleaned!;
    const pointer = normalizeDefsPointer(defPointer);

    let node: any = root;
    if (pointer !== '#') {
        if (!pointer.startsWith('#/')) {
            throw new Error(`Invalid pointer: ${pointer}`);
        }
        const segments = pointer.slice(2).split('/').map(jsonPointerUnescape);
        for (const seg of segments) {
            if (node == null || typeof node !== 'object' || !(seg in node)) {
                throw new Error(`Pointer not found: ${pointer}`);
            }
            node = node[seg];
        }
    }

    return clone ? JSON.parse(JSON.stringify(node)) : node;
}

export function getPrimitiveRef(defPointer: string): string {
    if (defPointer == null || defPointer === '') {
        throw new Error('A JSON Pointer must be provided. Use "#" for the top-level schema or one of: "FormatData", "#/$defs/FormatData", "/$defs/FormatData", "$defs/FormatData".');
    }
    if (!primitivesRootSchemaCleaned) getAjvPrimitive();
    const rootId = (primitivesRootSchemaCleaned as any)?.$id ?? 'PrimitivesSchema';
    const pointer = normalizeDefsPointer(defPointer);
    return pointer === '#' ? rootId : `${rootId}${pointer}`;
}






