import type { Primitive<PERSON><PERSON><PERSON><PERSON>, ExposedSchema<PERSON>son, TypeDataJson, SignatureMetaJson, SignatureDataJson, WorkStepJson } from '@toolproof/_schemas';
import { CONSTANTS } from '@toolproof/_lib/constants';
import type { UIContext } from './types/types.js';
import type { ErrorObject } from 'ajv';
import { getPrimitiveValidator, getPrimitiveRef, getResourceValidator } from './compilers.js';
// import { generateJobSchemaOverlay, generateJobStepSchemaOverlay, generateResourceSchemaMapOverlay } from './overlays.js';


export function validatePrimitive(data: PrimitiveDataJson, pointer: string, uiContext: UIContext) {
    try {
        // Compile by absolute $ref rather than compiling the raw subschema object
        const ref = getPrimitiveRef(pointer); // e.g., https://.../Primitives.json#/$defs/FormatData
        const schema = { $ref: ref };
        const { validate } = getPrimitiveValidator(schema, `__ref:${ref}`);
        const ok = (validate as (this: UIContext, data: unknown) => boolean).call(
            uiContext,
            data
        );
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}

/* export function validateJob(job: JobDataJson, roles: RoleMetaJson[], uiContext: UIContext) {
    try {
        // Start from a wrapper that references the base JobData schema
        const baseRef = getPrimitiveRef(CONSTANTS.SCHEMA.JobData);
        const base = { allOf: [{ $ref: baseRef }] };

        // Apply overlay by appending constraints to allOf
        const JobSchemaOverlay = generateJobSchemaOverlay(base, roles, job.isPredicate || false);

        // console.log('JobSchemaOverlay:', JSON.stringify(JobSchemaOverlay, null, 2));

        // Build a cache key that changes when roles or predicate flag change
        const roleKey = roles.map(r => r.id).sort().join(',');
        const overlayKey = `__jobOverlay:${job.isPredicate ? 1 : 0}:${roleKey}`;

        const { validate } = getPrimitiveValidator(JobSchemaOverlay, overlayKey);
        const ok = (validate as (this: UIContext, data: unknown) => boolean).call(
            uiContext,
            job
        );
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}

export function validateJobStep(jobStep: JobStepJson, jobs: JobMetaJson[], uiContext: UIContext) {
    try {
        // Start from a wrapper that references the base JobStep schema
        const baseRef = getPrimitiveRef(CONSTANTS.SCHEMA.JobStep);
        const base = { allOf: [{ $ref: baseRef }] };

        // Apply overlay by appending constraints to allOf
        const JobStepSchemaOverlay = generateJobStepSchemaOverlay(base, jobStep, jobs);

        // console.log('JobStepSchemaOverlay:', JSON.stringify(JobStepSchemaOverlay, null, 2));

        // Build a cache key that changes when jobs change
        const jobKey = jobs.map(j => j.id).sort().join(',');
        const overlayKey = `__jobStepOverlay:${jobKey}`;

        const { validate } = getPrimitiveValidator(JobStepSchemaOverlay, overlayKey);
        const ok = (validate as (this: UIContext, data: unknown) => boolean).call(
            uiContext,
            jobStep
        );
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}

export function validateResourceMap(jobStep: JobStepJson, roles: RoleMetaJson[], types: TypeDataJson[], uiContext: UIContext) {
    try {
        // Start from a wrapper that references the base ResourceMap schema
        const baseRef = getPrimitiveRef(CONSTANTS.SCHEMA.ResourceMap);
        const base = { allOf: [{ $ref: baseRef }] };

        // Apply overlay by appending constraints to allOf
        const ResourceMapSchemaOverlay = generateResourceSchemaMapOverlay(base, jobStep, roles, types);

        // console.log('ResourceMapSchemaOverlay:', JSON.stringify(ResourceMapSchemaOverlay, null, 2));

        // Build a cache key that changes when roles or types change
        const roleKey = roles.map(r => r.id).sort().join(',');
        const typeKey = types.map(t => t.id).sort().join(',');
        const overlayKey = `__resourceMapOverlay:${roleKey}:${typeKey}`;

        const { validate } = getPrimitiveValidator(ResourceMapSchemaOverlay, overlayKey);
        const ok = (validate as (this: UIContext, data: unknown) => boolean).call(
            uiContext,
            jobStep
        );
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
} */

export function validateResource(
    exposedSchema: ExposedSchemaJson,
    resource: unknown
) {
    try {
        const validate = getResourceValidator(exposedSchema);
        const ok = validate(resource) as boolean;
        return { isValid: ok, errors: (validate.errors as ErrorObject[]) ?? null };
    } catch (e) {
        const err: ErrorObject = {
            instancePath: '',
            schemaPath: '',
            keyword: 'compile',
            params: {},
            message: (e as Error).message,
        } as ErrorObject;
        return { isValid: false, errors: [err] };
    }
}
