import { CONSTANTS } from '_lib/dist/constants.js';
import * as CONSTANTS_LOCAL from '../constants.js';
import { GraphState } from '../types.js';
import { GraphStateAnnotationRoot } from '../graphState.js';
import { NodeLoadPrimitives } from '../nodes/NodeLoadPrimitives.js';
import { NodeWorkStep } from '../nodes/NodeWorkStep.js';
import { NodeBranchStep } from 'src/nodes/NodeBranchStep.js';
import { NodeWhileStep } from '../nodes/NodeWhileStep.js';
import { NodeForStep } from '../nodes/NodeForStep.js';
import { StateGraph, START, END } from '@langchain/langgraph';


const EdgeRouting = (state: GraphState) => {

    /* console.log('EgeRouting', JSON.stringify([...state.primitivesLib.implementations], null, 2));

    return END; */

    if (state.stepCounter >= state.workflowSpec.workflow.steps.length) {
        return END;
    }

    const step = state.workflowSpec.workflow.steps[state.stepCounter];

    if (step.kind === CONSTANTS.STEP.work) {
        return CONSTANTS_LOCAL.NODE_Work_Step;
    } else if (step.kind === CONSTANTS.STEP.branch) {
        return CONSTANTS_LOCAL.NODE_BRANCH_STEP;
    }
    else if (step.kind === CONSTANTS.STEP.while) {
        return CONSTANTS_LOCAL.NODE_WHILE_STEP;
    } else if (step.kind === CONSTANTS.STEP.for) {
        return CONSTANTS_LOCAL.NODE_FOR_STEP;
    }

    return END;

};


const stateGraph = new StateGraph(GraphStateAnnotationRoot)
    .addNode(
        CONSTANTS_LOCAL.NODE_LOAD_PRIMITIVES,
        new NodeLoadPrimitives()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_Work_Step,
        new NodeWorkStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_BRANCH_STEP,
        new NodeBranchStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_WHILE_STEP,
        new NodeWhileStep()
    )
    .addNode(
        CONSTANTS_LOCAL.NODE_FOR_STEP,
        new NodeForStep()
    )
    .addEdge(START, CONSTANTS_LOCAL.NODE_LOAD_PRIMITIVES)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_LOAD_PRIMITIVES, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_Work_Step, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_BRANCH_STEP, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_WHILE_STEP, EdgeRouting)
    .addConditionalEdges(CONSTANTS_LOCAL.NODE_FOR_STEP, EdgeRouting)


export const graph = stateGraph.compile();



