import type { WorkStep as <PERSON><PERSON><PERSON><PERSON><PERSON>, ResourceMap as ResourceMap<PERSON><PERSON>, JsonValue as <PERSON>sonValue<PERSON><PERSON> } from '_schemas/dist/types/types.js';
import * as CONSTANTS_LOCAL from '../constants.js';
import { bar } from '../_lib/ajvWrapper.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';
import axios from 'axios';


const nodeName = CONSTANTS_LOCAL.NODE_Work_Step;


export class NodeWorkStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {
        try {
            const workStep = state.workflowSpec.workflow.steps[state.stepCounter] as WorkStepJson; // safe to assert as WorkStep<PERSON><PERSON> because EdgeRouting ensures that only WorkSteps reach here
            const execution = workStep.execution;

            const inputBindings = execution.resourceBindings.inputBindings;

            const outputBindings = execution.resourceBindings.outputBindings;

            const resourceMap = state.workflowSpec.resourceMaps?.[0] ?? {};

            let payload: { [key: string]: string } = {};

            Object.entries(inputBindings).forEach(([key, value]) => {

                const resourceMeta = resourceMap[value];

                // Default path if nothing can be resolved
                let path = 'TYPE-wSo0cBZk3yK9F5DUb9zV/5cba672cfb03925544d36d23fe7f8e3b9c077697564511c0b0c8da4b33751a56'; // ATTENTION: hardcoded for now, represents integer 0

                if (resourceMeta && typeof resourceMeta === 'object') {
                    if ((resourceMeta.path && typeof resourceMeta.path === 'string')) {
                        path = resourceMeta.path;
                    } else if (resourceMeta.pointer && typeof resourceMeta.pointer === 'string') {
                        // Resolve pointer by matching entries that share the same prefix (before '__')
                        const pointerPrefix = String(resourceMeta.pointer).split('__')[0];
                        const candidates = Object.entries(resourceMap).filter(([k]) => k.startsWith(pointerPrefix));

                        // Choose the candidate with the most recent timestamp
                        let bestPath: string | null = null;
                        let bestTs = Number.NEGATIVE_INFINITY;

                        for (const [, candidateVal] of candidates) {
                            const ts = candidateVal.timestamp;
                            // Timestamps are ISO strings like '2025-09-15T23:26:02.993Z'. Use Date.parse
                            // to convert to milliseconds; if timestamp is already numeric, accept it.
                            const tsNum = typeof ts === 'number' ? ts : Date.parse(String(ts));
                            if (!Number.isNaN(tsNum) && tsNum > bestTs) {
                                bestTs = tsNum;
                                if ((candidateVal as any).path) {
                                    bestPath = (candidateVal as any).path;
                                }
                            }
                        }

                        if (bestPath) {
                            path = bestPath;
                        }
                    } else {
                        throw new Error(`Entry for resource key '${value}' does not have a valid 'path' or 'pointer'`);
                    }
                }

                payload[key] = path;
            });

            Object.entries(outputBindings).forEach(([key, value]) => {
                payload[key] = value; // For outputs, just pass through the binding key (the job service will use this to name outputs)
            });

            console.log('payload:', JSON.stringify(payload, null, 2));

            const asyncWrapper = async (url: string): Promise<ResourceMapJson> => {

                const response = await axios.post(
                    url,
                    payload,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        timeout: 30 * 60 * 1000, // 30 minutes in milliseconds
                    }
                );

                const result = response.data;

                // console.log('result:', JSON.stringify(result, null, 2));

                return result.outputs;
            }

            const implementation = state.primitivesLib.implementations.get(execution.implementationId);
            if (!implementation) {
                throw new Error(`Implementation with ID ${execution.implementationId} not found in primitivesLib.implementations`);
            }

            // console.log('implementation:', JSON.stringify(implementation, null, 2));

            const outputMap = await asyncWrapper(implementation.uri);

            // Here, for each output we must invoke the respective ResourceType's extractor job
            await Promise.all(Object.entries(outputMap).map(async ([outputRole, output]) => {

                const pathStr = typeof (output as any)?.path === 'string' ? (output as any).path : String((output as any)?.path ?? '');
                const exposedData = await bar(pathStr);

                // console.log('outputRole:', JSON.stringify(outputRole, null, 2));

                // Merge the extracted data with the output
                outputMap[outputRole] = {
                    ...(output as any),
                    exposedData: exposedData as JsonValueJson
                };

            }));

            // Now outputs have the exposedData property added

            // Create new entries for resourceMaps[0] based on outputBindings
            const newResourceMapEntries: ResourceMapJson = {};

            // Map job output roles to bound keys using the result paths
            Object.entries(outputBindings).forEach(([outputRole, boundKey]) => {
                if (outputMap[outputRole]) {
                    newResourceMapEntries[boundKey] = outputMap[outputRole];
                }
            });

            return {
                messages: [new AIMessage(`${nodeName} completed`)],
                workflowSpec: {
                    ...state.workflowSpec,
                    resourceMaps: [
                        {
                            ...state.workflowSpec.resourceMaps[0],
                            ...newResourceMapEntries
                        },
                        ...state.workflowSpec.resourceMaps.slice(1)
                    ]
                },
                stepCounter: state.stepCounter + 1
            };

        } catch (error: any) {
            // Structured logging to help diagnose HTTP / Axios errors (ERR_BAD_REQUEST, etc.)
            try {
                // Use Axios config data (if present) rather than referencing local variables that
                // may be out of scope when the catch runs.
                const payloadPreview = error?.config?.data ?? null;
                console.error(`Error in ${nodeName}:`, {
                    message: error?.message,
                    code: error?.code,
                    status: error?.response?.status,
                    responseData: error?.response?.data,
                    requestUrl: error?.config?.url,
                    payload: payloadPreview,
                });
            } catch (logErr) {
                // Fallback if structured logging throws
                console.error(`Error in ${nodeName} (logging failed):`, error);
            }

            return {
                messages: [new AIMessage(`${nodeName} failed`)],
                workflowSpec: {
                    ...state.workflowSpec,
                    stepCounter: state.stepCounter + 1
                },
            };
        }
    }
}



