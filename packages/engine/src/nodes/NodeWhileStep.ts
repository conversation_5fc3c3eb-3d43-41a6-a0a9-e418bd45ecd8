import * as CONSTANTS from '_lib/dist/constants.js';
import * as CONSTANTS_LOCAL from '../constants.js';
import { getNewId } from '_lib/dist/firestoreAdminHelpers.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_WHILE_STEP;


export class NodeWhileStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        try {
            const step = state.workflowSpec.workflow.steps[state.stepCounter];
            if (step.kind !== CONSTANTS.WHILE) {
                throw new Error(`Expected a ${CONSTANTS.WHILE} step at stepCounter ${state.stepCounter}, but got '${step.kind}'`);
            }
            const condition = step.case.when;

            // Evaluate condition.resource prefix (part before "__")
            const resourceStr = typeof condition.resource === 'string' ? condition.resource : '';
            if (!resourceStr || !resourceStr.includes('__')) {
                throw new Error(`Invalid condition.resource format: '${condition.resource}'`);
            }
            const prefix = resourceStr.split('__')[0];

            // Per instructions, iterate through state.workflowSpec.resourceMaps[0]
            const resourceMap = state.workflowSpec.resourceMaps?.[0] ?? {};
            const keys = Object.keys(resourceMap);

            console.log('keys', JSON.stringify(keys, null, 2));

            // Only consider keys that start with the prefix (before '__')
            const matchingKeys = keys.filter(k => k.startsWith(prefix));
            const targetSemantic = Number(condition.semanticIdentity);

            console.log(`${nodeName}: prefix='${prefix}', matchingKeys=${JSON.stringify(matchingKeys)}, targetSemantic=${targetSemantic}`);

            // Choose the matching entry with the most recent timestamp, then compare its semanticIdentity
            let chosenSem = Number.NEGATIVE_INFINITY;
            if (matchingKeys.length > 0) {
                let latestTs = Number.NEGATIVE_INFINITY;
                let latestEntry: any = null;
                for (const key of matchingKeys) {
                    const resourceMeta = resourceMap[key];
                    const ts = (resourceMeta && (resourceMeta as any).timestamp);
                    // Timestamps are ISO strings like '2025-09-15T23:26:02.993Z'. Use Date.parse
                    // to convert to ms; accept numeric timestamps unchanged.
                    const tsNum = typeof ts === 'number' ? ts : Date.parse(String(ts));
                    console.log(`${nodeName}: key='${key}', entry=${JSON.stringify(resourceMeta)}, ts=${ts}, tsNum=${tsNum}`);
                    if (!Number.isNaN(tsNum) && tsNum > latestTs) {
                        latestTs = tsNum;
                        latestEntry = resourceMeta;
                    }
                }

                if (latestEntry) {
                    const sem = latestEntry?.exposedData?.semanticIdentity;
                    const semNum = typeof sem === 'string' ? Number(sem) : sem;
                    if (typeof semNum === 'number' && !Number.isNaN(semNum)) {
                        chosenSem = semNum;
                    }
                }
            }

            const currentSpec = state.workflowSpec;

            // If there's no chosen semanticIdentity or it's not greater than target -> continue (true)
            const hasGreater = typeof chosenSem === 'number' && !Number.isNaN(chosenSem) && chosenSem > targetSemantic;

            console.log(`${nodeName}: chosenSem=${chosenSem}, hasGreater=${hasGreater}`);

            if (!hasGreater) {
                const steps = Array.isArray(currentSpec.workflow?.steps) ? [...currentSpec.workflow.steps] : [];
                const whileStep = {
                    ...step,
                    id: getNewId(CONSTANTS.WHILE)
                }
                const WorkStep = {
                    ...step.case.what,
                    id: getNewId(CONSTANTS.JOB)
                }
                const insertIndex = state.stepCounter + 1;
                console.log(`${nodeName}: inserting job at index ${insertIndex}`);
                steps.splice(insertIndex, 0, WorkStep, whileStep);

                const updatedWorkflow = {
                    ...currentSpec.workflow,
                    steps
                };

                const updatedSpec = {
                    ...currentSpec,
                    workflow: updatedWorkflow
                };

                return {
                    messages: [new AIMessage(`${nodeName}: condition true (chosen ${chosenSem}), inserting job and while steps at index ${insertIndex}`)],
                    workflowSpec: updatedSpec,
                    stepCounter: state.stepCounter + 1
                };
            }

            // Otherwise there exists an entry with semanticIdentity > target -> exit the while by incrementing stepCounter
            const nextCounter = state.stepCounter + 1;
            return {
                messages: [new AIMessage(`${nodeName}: condition false (chosen ${chosenSem} > ${targetSemantic}), incrementing stepCounter to ${nextCounter}`)],
                workflowSpec: currentSpec,
                stepCounter: nextCounter
            };

        } catch (error: any) {
            throw new Error(`Error in ${nodeName}: ${error.message}`);
        }
    }

}