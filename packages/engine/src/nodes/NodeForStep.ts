import { CONSTANTS } from '_lib/dist/constants.js';
import * as CONSTANTS_LOCAL from '../constants.js';
import { getNewId } from '_lib/dist/firestoreAdminHelpers.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_FOR_STEP;


export class NodeForStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        try {
            const step = state.workflowSpec.workflow.steps[state.stepCounter];
            if (step.kind !== CONSTANTS.STEP.for) {
                throw new Error(`Expected a ${CONSTANTS.STEP.for} step at stepCounter ${state.stepCounter}, but got '${step.kind}'`);
            }
            const predicateStep = step.case.when;

            const interationTarget = state.workflowSpec.resourceMaps?.[0]?.[predicateStep.resourceBindings.inputBindings[CONSTANTS.ROLE_LESS_THAN_TARGET]].exposedData?.semanticIdentity; // ATTENTION

            const currentSpec = state.workflowSpec;

            const shouldStop = state.iterationCounter >= interationTarget; // ATTENTION: This is effectively the less-than job

            if (!shouldStop) {
                const steps = Array.isArray(currentSpec.workflow?.steps) ? [...currentSpec.workflow.steps] : [];
                const forStep = {
                    ...step,
                    id: getNewId(CONSTANTS.STEP.for)
                }
                const WorkStep = {
                    ...step.case.what,
                    id: getNewId('WORKSTEP') // ATTENTION: hardcoded kind
                }
                const insertIndex = state.stepCounter + 1;
                console.log(`${nodeName}: inserting job at index ${insertIndex}`);
                steps.splice(insertIndex, 0, WorkStep, forStep);

                const updatedWorkflow = {
                    ...currentSpec.workflow,
                    steps
                };

                const updatedSpec = {
                    ...currentSpec,
                    workflow: updatedWorkflow
                };

                return {
                    messages: [new AIMessage(`${nodeName}: condition true (iterationCounter: ${state.iterationCounter}), inserting job and for steps at index ${insertIndex}`)],
                    workflowSpec: updatedSpec,
                    stepCounter: state.stepCounter + 1,
                    iterationCounter: state.iterationCounter + 1
                };
            }

            const nextCounter = state.stepCounter + 1;
            return {
                messages: [new AIMessage(`${nodeName}: condition false (iterationCounter: ${state.iterationCounter}), incrementing stepCounter to ${nextCounter}`)],
                workflowSpec: currentSpec,
                stepCounter: nextCounter,
                iterationCounter: 0
            };

        } catch (error: any) {
            throw new Error(`Error in ${nodeName}: ${error.message}`);
        }
    }

}