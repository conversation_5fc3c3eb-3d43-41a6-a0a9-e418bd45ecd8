import type { WorkStep as WorkStepJson } from '_schemas/dist/types/types.js';
import { BaseNode, GraphState } from '../types.js';
import * as CONSTANTS from '_lib/dist/constants.js';
import * as CONSTANTS_LOCAL from '../constants.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_BRANCH_STEP;


export class NodeBranchStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        try {
            const step = state.workflowSpec.workflow.steps[state.stepCounter];
            if (step.kind !== CONSTANTS.BRANCH) {
                throw new Error(`Expected a ${CONSTANTS.BRANCH} step at stepCounter ${state.stepCounter}, but got '${step.kind}'`);
            }
            const conditionalWrappers = step.cases;

            let WorkStep: WorkStepJson | null = null;
            for (const wrapper of conditionalWrappers) {
                if (wrapper.when.hack) {
                    WorkStep = wrapper.what;
                    break;
                }
            }

            const currentSpec = state.workflowSpec;

            if (WorkStep) {
                // Insert the chosen job right after the current branch step
                const steps = Array.isArray(currentSpec.workflow?.steps) ? [...currentSpec.workflow.steps] : [];
                const insertIndex = state.stepCounter + 1;
                steps.splice(insertIndex, 0, WorkStep); // WorkStep already has an id

                const updatedWorkflow = {
                    ...currentSpec.workflow,
                    steps
                };
                const updatedSpec = {
                    ...currentSpec,
                    workflow: updatedWorkflow
                };

                return {
                    messages: [new AIMessage(`${nodeName}: matched case, inserted job at index ${insertIndex}`)],
                    workflowSpec: updatedSpec,
                    stepCounter: insertIndex
                };
            }

            const nextCounter = state.stepCounter + 1;

            return {
                messages: [new AIMessage(`${nodeName}: no matching case, incrementing stepCounter to ${nextCounter}`)],
                workflowSpec: currentSpec,
                stepCounter: nextCounter
            };

        } catch (error: any) {
            throw new Error(`Error in ${nodeName}: ${error.message}`);
        }
    }

}