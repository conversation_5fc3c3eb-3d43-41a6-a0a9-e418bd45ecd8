import type { TypeMeta as TypeMeta<PERSON>son, ImplementationMeta as ImplementationMetaJson } from '_schemas/dist/types/types.js';
import { CONSTANTS } from '_lib/dist/constants.js';
import { listPrimitives } from '_lib/dist/firestoreAdminHelpers.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


export class NodeLoadPrimitives extends BaseNode {

    constructor() {
        super('NodeLoadPrimitives');
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        if (state.stepCounter === 0) {
            try {
                const asyncWrapper = async () => {

                    const [typesMeta, implementationsMeta] = await Promise.all([
                        listPrimitives<TypeMetaJson>(CONSTANTS.PRIMITIVES.types),
                        listPrimitives<ImplementationMetaJson>(CONSTANTS.PRIMITIVES.implementations),
                    ]);

                    const primitivesLib = {
                        [CONSTANTS.PRIMITIVES.types]: new Map(typesMeta.items.map(item => [item.id, item])),
                        [CONSTANTS.PRIMITIVES.implementations]: new Map(implementationsMeta.items.map(item => [item.id, item])),
                    };

                    return primitivesLib;
                }

                const primitivesLib = await asyncWrapper();

                return {
                    messages: [new AIMessage('Primitives loaded successfully')],
                    primitivesLib
                };
            } catch (error: any) {
                throw new Error(`Error in NodeLoadPrimitives: ${error.message}`);
                /* console.error('Error in NodeLoadPrimitives:', error);
                return {
                    messages: [new AIMessage('NodeLoadPrimitives failed')],
                    primitivesLib: new Map()
                }; */
            }
        } else {
            throw new Error('NodeLoadPrimitives can only be run at the start of the workflow');
        }
    }

}



