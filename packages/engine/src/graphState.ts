import type { WorkflowSpec as WorkflowSpecJson } from '_schemas/dist/types/types.d.ts';
import type { DryRunManagerType } from '_lib/dist/types.js';
import type { PrimitivesLib } from './types.js';
import { Annotation, MessagesAnnotation } from '@langchain/langgraph';


export const GraphStateAnnotationRoot = Annotation.Root({
    ...MessagesAnnotation.spec,
    dryModeManager: Annotation<DryRunManagerType>(
        {
            reducer: (prev, next) => next,
            default: () => ({
                dryRunMode: false,
                delay: 0,
                drySocketMode: false,
            }),
        }
    ),
    workflowSpec: Annotation<WorkflowSpecJson>(),
    // primitivesLib is an object with three Map values keyed by logical category.
    primitivesLib: Annotation<PrimitivesLib>(),
    stepCounter: Annotation<number>(
        {
            reducer: (prev, next) => next,
            default: () => 0
        }
    ),
    iterationCounter: Annotation<number>(
        {
            reducer: (prev, next) => next,
            default: () => 0
        }
    ),
});