{"name": "engine", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start:GraphRunWorkflow": "cross-env NODE_ENV=GraphRunWorkflow node --loader ts-node/esm src/index.ts"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@babel/generator": "^7.27.3", "@babel/parser": "^7.27.3", "@babel/traverse": "^7.27.3", "@google-cloud/storage": "^7.14.0", "@langchain/core": "^0.3.62", "@langchain/langgraph": "^0.3.6", "@langchain/langgraph-sdk": "^0.0.74", "@langchain/openai": "^0.4.4", "@toolproof/_lib": "file:./_lib", "@types/uuid": "^10.0.0", "ajv": "^8.17.1", "axios": "^1.10.0", "dotenv": "^16.4.7", "firebase-admin": "^13.2.0", "openai": "^4.87.3", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@types/babel__generator": "^7.27.0", "@types/babel__traverse": "^7.20.7", "@types/ws": "^8.18.1", "cross-env": "^10.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}